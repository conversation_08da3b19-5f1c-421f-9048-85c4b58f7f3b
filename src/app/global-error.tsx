"use client";

import { MultipleSessionError } from "@/_core/lib/context/error";
import { defaultRemedy, multipleSessionErrorRemedy } from "@/_core/ui/remedies";

const errorMap = {
  [MultipleSessionError.name]: multipleSessionErrorRemedy,

  // Errores no mapeados
  default: defaultRemedy,
};

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const Component = errorMap[error.name] || errorMap.default;

  return (
    <html>
      <body>
        <div className="min-h-screen flex flex-col">
          <header className="h-14 bg-purple-300 sticky top-0 z-50 flex items-center px-4">
            Header
          </header>
          <main className="flex-1 bg-green-300 flex items-center justify-center">
            <Component error={error} reset={reset} />
          </main>
        </div>
      </body>
    </html>
  );
}
