import React from "react";
import ReadCollectionRenderer from "@/_core/ui/components/CRUD/readCollection/readCollectionRenderer";
import { Typography, Stack } from "@mui/material";
import CreateButton from "@/_core/ui/components/button/createButton";
import { DataRow } from "@/_core/utils/crud";
import { ColumnConfigWithStringRenderer } from "@/_core/ui/components/CRUD/readCollection/readCollectionRenderer";

import { getSubmitActionFor, ServerActionParams } from "@/_lib/data/model/action/actionFactory";

const ENTITY = "asociados";
const ACTION = "collection";

const columnsConfig: ColumnConfigWithStringRenderer[] = [
  {
    key: "profesional",
    label: "PROFESIONAL",
    renderer: "navigate",
    options: {
      basePath: "/asociados",
      paramKey: "id",
    },
  },
  { key: "matricula", label: "MATRICU<PERSON>", renderer: "integer" },
  { key: "localidad", label: "LOCALIDAD", renderer: "default" },
  { key: "cuit", label: "CUIT", renderer: "default" },
  {
    key: "estado",
    label: "ESTADO",
    renderer: "badge",
    options: {
      bg: "bg-gray-100",
      text: "text-gray-700",
    },
  },
  { key: "__actions", label: "", renderer: "actions" },
];

export default async function AsociadosPage() {
  // Get the action function from the action factory
  const action = await getSubmitActionFor(ENTITY, ACTION);
  
  // Execute the action to fetch data
  const {data, meta} = await action({} as ServerActionParams) as { data: DataRow[]; meta: unknown };
  

  return (
    <main>
      <Stack direction="row" justifyContent="space-between">
        <Typography
          sx={{ typography: { xs: "h6", md: "h6xl", lg: "h2xl" } }}
          className="pl-4"
          color="text.primary"
        >
          Asociados
        </Typography>
        <CreateButton entityName="asociados" />
      </Stack>
      <ReadCollectionRenderer data={data} columnsConfig={columnsConfig} />
    </main>
  );
}
