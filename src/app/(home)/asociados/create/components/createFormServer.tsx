"use server"
import { getSubmitActionFor } from "@/_lib/data/model/action/actionFactory";

import { metaCreateBody, stepsBody } from "../meta/metaCreateBody";
import { CreateFormClient } from "./createFormClient";

const ENTITY = "asociados";
const ACTION = "create";

export async function CreateFormServer() {

  
  return (
    <CreateFormClient
      entityName={ENTITY}
      actionName={ACTION}
      meta={metaCreateBody}
      steps={stepsBody}
    />
  );
}
