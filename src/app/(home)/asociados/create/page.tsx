"use server";

import Link from "next/link";
import { Breadcrumbs, Typography } from "@mui/material";
import { PermissionGuard } from "@/_core/ui/components/permissonGuard/permissionGuard";
import { CreateFormServer } from "./components/createFormServer";

export default async function Dashboard() {
  return (
    <div className="p-6 space-y-4">
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb">
        <Link className="hover:underline" href="/dashboard" color="inherit">
          Home
        </Link>
        <Link className="hover:underline" href="/asociados" color="inherit">
          Asociados
        </Link>
        <Typography color="inherit" className="pointer-events-none select-none">
          Nuevo
        </Typography>
      </Breadcrumbs>

      {/* Formulario */}
      <PermissionGuard entity="asociados" action="create" fallback={<></>}>
        <CreateFormServer />
      </PermissionGuard>
    </div>
  );
}
