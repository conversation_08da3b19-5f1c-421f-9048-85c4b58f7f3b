import { FieldMeta } from "@/_core/ui/components/forms/types";
import { InferSchema } from "@/_lib/data/model/schema";
import { Step } from "@/_core/ui/components/forms/types";

export const metaCreateBody: Record<
  keyof InferSchema<"asociados", "create">,
  FieldMeta
> = {
  // PASO 1.
  //Datos personales
  datosPersonales: {
    type: "group",
    fields: {
      apellido: {
        label: "Apellido",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      nombre: {
        label: "Nombre",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      fechaNacimiento: {
        label: "Fecha de nacimiento",
        type: "date",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      dni: {
        label: "DNI",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
    },
  },
  //Datos de contacto
  datosContacto: {
    type: "group",
    label: "Contacto",
    fields: {
      'domicilio.telefonoCodigoArea': {
        label: "Código de área",
        type: "text",
        grid: { xs: 3, md: 3, lg: 3, xl: 3 },
      },
      'domicilio.telefonoNumero': {
        label: "Número de teléfono",
        type: "text",
        grid: { xs: 9, md: 9, lg: 9, xl: 9 },
      },
      'domicilio.email': {
        label: "Email",
        type: "text",
      },
    },
  },
  //Datos de domicilio particular
  datosDomicilioParticular: {
    type: "group",
    label: "Domicilio particular",
    fields: {
      'domicilio.calle': {
        label: "Calle",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      'domicilio.numero': {
        label: "Número",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      'domicilio.piso': {
        label: "Piso",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      'domicilio.dpto': {
        label: "Dpto",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      'domicilio.localidadId': {
        label: "Localidad",
        type: "asyncSelect",
        entity: "localidades",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
    },
  },

  // PASO 2.
  //Datos de matrícula
  datosMatricula: {
    type: "group",
    fields: {
      'matricula.numero': {
        label: "Número de matrícula",
        type: "integer",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      'matricula.fechaVencimiento': {
        label: "Fecha de vencimiento",
        type: "date",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      'matricula.fechaEmisionTitulo': {
        label: "Fecha de emisión del título",
        type: "date",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
    },
  },
};

export const stepsBody: Step<"asociados", "create">[] = [
  {
    label: "Datos personales",
    fields: [
      "datosPersonales",
      "datosContacto",
      "datosDomicilioParticular",
    ],
  },
  {
    label: "Matrícula/Título",
    fields: [
      "datosMatricula",
    ],
  },
];
