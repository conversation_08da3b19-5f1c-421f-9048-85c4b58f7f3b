"use server";

import { Box } from "@mui/material";
import OneTabOnlyGuard from "@/_core/ui/components/oneTabOnlyGuard/oneTabOnlyGuard";
import { MenuProvider } from "./components/aside/menuContext";
import { Header } from "./components/header/header";
import { AsideMobile } from "./components/aside/asideMobile";
import { AsideDesktop } from "./components/aside/asideDesktop";

export default async function HomeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MenuProvider>
      <Box
        className="flex min-h-screen overflow-x-hidden"
        sx={{ bgcolor: "background.default" }}
      >
        <OneTabOnlyGuard />

        <AsideMobile />
        <AsideDesktop />

        <div className="flex flex-col flex-1">
          <Header />
          <div className="flex flex-1 flex-col 2xl:flex-row">
            <main className="flex-1 py-8 pl-4 pr-8 h-[calc(100vh-4.5rem)] max-h-[calc(100vh-4.5rem)] overflow-y-auto custom-scrollbar">
              {children}
            </main>
          </div>
        </div>
      </Box>
    </MenuProvider>
  );
}
