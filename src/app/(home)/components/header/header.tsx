"use client";

import { IconButton, Paper } from "@mui/material";
import { Menu } from "react-feather";
import { AccountCircle } from "@/_ui/icons/components";
import Image from "next/image";
import Link from "next/link";
import { useMenu } from "../aside/menuContext";

export function Header() {
  const { toggleMenu } = useMenu();

  return (
    <header className="sticky top-0">
      <Paper
        elevation={10}
        className="h-18 px-12 flex items-center justify-between lg:justify-end"
        sx={{
          boxShadow: "0px 0px 4px rgba(0, 0, 0, 0.1)",
          bgcolor: "background.paper",
        }}
      >
        <IconButton className="block lg:hidden" onClick={toggleMenu}>
          <Menu size={24} />
        </IconButton>

        <Link href="/dashboard" className="block lg:hidden">
          <Image src="/bqn_logo.svg" alt="Logo" width={35} height={35} />
        </Link>

        <IconButton>
          <AccountCircle size={35} />
        </IconButton>
      </Paper>
    </header>
  );
}
