"use client";

import React from "react";
import { LinkItem, HeaderItem, ExpandableItem } from "./sideBarItems";
import { RouteItem } from "./meta/types";

type Props = {
  item: RouteItem;
  level?: number;
};

export default function RouteDispatcher({ item, level = 0 }: Props) {
  switch (item.type) {
    case "link":
      return <LinkItem item={item} level={level} />;
    case "header":
      return <HeaderItem item={item} />;
    case "expandable":
      return <ExpandableItem item={item} />;
    default:
      return null;
  }
}
