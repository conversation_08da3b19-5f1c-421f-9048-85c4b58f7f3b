
import type { IconKey } from "@/_ui/icons/iconMap";

export type RouteItem = RouteLink | RouteHeader | RouteExpandable;

export type RouteLink = {
  type: "link";
  icon?: IconKey;
  label: string;
  href: string;
};

export type RouteHeader = {
  type: "header";
  label: string;
  childs?: RouteItem[];
};

export type RouteExpandable = {
  type: "expandable";
  icon?: IconKey;
  label: string;
  childs?: RouteItem[];
};
