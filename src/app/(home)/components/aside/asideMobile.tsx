"use client";

import { Drawer } from "@mui/material";
import { Navbar } from "./navbar";
import { routes } from "./meta/routes";
import { useMenu } from "./menuContext";

export function AsideMobile() {
  const { isOpen, closeMenu } = useMenu();

  return (
    <aside className="block lg:hidden">
      <Drawer
        anchor="left"
        open={isOpen}
        onClose={closeMenu}
        variant="temporary"
        sx={{ display: { lg: "none" } }}
      >
        <div className="h-screen max-h-screen p-7 px-1 w-[calc(75vw)] md:w-[calc(50vw)]">
          <Navbar routes={routes} />
        </div>
      </Drawer>
    </aside>
  );
}
