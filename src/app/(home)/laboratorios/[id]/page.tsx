"use client";
import ReadIndexRenderer from "@/_core/ui/components/CRUD/readIndex/readIndexRenderer";
import EstadoUpdaterButton from "@/_core/ui/components/button/estadoUpdaterButton";
import HistoricalAccordion from "@/_core/ui/components/acordion/historicalAcordion";
import { Breadcrumbs, Stack, Typography } from "@mui/material";
import Link from "next/link";
import EditableTab from "@/_core/ui/components/tab/editableTab";

export default function LaboratorioPage() {
  return (
    <div className="p-6 space-y-4">
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb">
        <Link className="hover:underline" href="/dashboard" color="inherit">
          Home
        </Link>
        <Link className="hover:underline" href="/laboratorios" color="inherit">
          Laboratorios
        </Link>
        <Typography color="inherit" className="pointer-events-none select-none">
          Nombre Laboratorio
        </Typography>
      </Breadcrumbs>

      {/* Detalle del laboratorio */}
      <ReadIndexRenderer
        title="Nombre Laboratorio"
        subtitle="CUIT: 27-15333457-5"
        badges={[
          {
            label: "Activo",
            bgColor: "#F2F4F8",
            textColor: "contrastText.main",
          },
        ]}
        actions={<EstadoUpdaterButton onEstadoUpdate={() => {}} />}
        tabs={[
          {
            label: "Datos",
            content: (
              <Stack direction="column" spacing={2}>
                <EditableTab>
                  <div className="text-sm space-y-2">
                    <Typography variant="body1" color="text.secondary">
                      <strong>RAZÓN SOCIAL:</strong> Nombre Laboratorio
                    </Typography>

                    <HistoricalAccordion
                      title="Historial"
                      items={[
                        { label: "dd/mm/aaaa", value: "INACTIVO" },
                        { label: "MOTIVO:", value: "Cierre" },
                        { label: "dd/mm/aaaa", value: "ACTIVO" },
                      ]}
                    />
                  </div>
                </EditableTab>
                
                <Stack direction="row" spacing={2}>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Domicilio Legal
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>CALLE:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>NÚMERO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>PISO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>DPTO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>LOCALIDAD:</strong>
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Contacto
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>TELÉFONO:</strong>2235-5628 75625
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>E-MAIL:</strong><EMAIL>
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                </Stack>

                <Stack direction={"column"} spacing={2} width={"100%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Datos fiscales / bancarios
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>CUIT:</strong>23-15333457-5
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>INGRESOS BRUTOS:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>CONDICIÓN IVA:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>CBU:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>BANCO:</strong>
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
              </Stack>
            ),
          },
          {
            label: "Sucursales",
            content: <div>Datos laborales</div>,
          },
          {
            label: "Cuenta",
            content: <div>Datos de cuenta</div>,
          },
          {
            label: "Suscripciones",
            content: <div>Opciones</div>,
          },
          {
            label: "Profesionales",
            content: <div>Profesionales</div>,
          },
          {
            label: "Trámites",
            content: <div>Historial de trámites</div>,
          },
        ]}
      />
    </div>
  );
}
