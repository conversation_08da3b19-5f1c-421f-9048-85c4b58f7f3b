"use client";

import React from "react";
import ReadCollectionRenderer, { ColumnConfig } from "@/_core/ui/components/CRUD/readCollection/readCollectionRenderer";
import { renderers } from "@/_core/ui/components/CRUD/readCollection/renderers";
import { Typography, Stack } from "@mui/material";
import CreateButton from "@/_core/ui/components/button/createButton";

const data = [
  {
    id: "1",
    razon_social: "Nombre Obra Social",
    codigo: "002",
    cuit: "27-15333457-5",
    iva: "Condición",
    localidad: "Aluminé",
  },
  {
    id: "2",
    razon_social: "Nombre Obra Social",
    codigo: "002",
    cuit: "27-15333457-5",
    iva: "Condición",
    localidad: "Aluminé",
  },
  {
    id: "3",
    razon_social: "Nombre Obra Social",
    codigo: "002",
    cuit: "27-15333457-5",
    iva: "Condición",
    localidad: "Aluminé",
  },
  {
    id: "4",
    razon_social: "Nombre Obra Social",
    codigo: "002",
    cuit: "27-15333457-5",
    iva: "Condición",
    localidad: "Aluminé",
  },
  {
    id: "5",
    razon_social: "Nombre Obra Social",
    codigo: "002",
    cuit: "27-15333457-5",
    iva: "Condición",
    localidad: "Aluminé",
  },
  {
    id: "6",
    razon_social: "Nombre Obra Social",
    codigo: "002",
    cuit: "27-15333457-5",
    iva: "Condición",
    localidad: "Aluminé",
  },
];

const columnsConfig: ColumnConfig[] = [
  {
    key: "razon_social",
    label: "RAZON SOCIAL",
    renderer: renderers.navigate,
    options: {
      basePath: "/obras_sociales",
      paramKey: "id",
    },
  },
  { key: "codigo", label: "CODIGO", renderer: renderers.default },
  { key: "cuit", label: "CUIT", renderer: renderers.default },
  { key: "iva", label: "IVA", renderer: renderers.default },
  { key: "localidad", label: "LOCALIDAD", renderer: renderers.default },
];

export default function ObrasSocialesPage() {
  return (
    <main>
      <Stack direction="row" justifyContent="space-between">
        <Typography
          sx={{ typography: { xs: "h6", md: "h6xl", lg: "h2xl" } }}
          className="pl-4"
          color="text.primary"
        >
          Obra Sociales
        </Typography>
        <CreateButton entityName="obras_sociales" />
      </Stack>
      <ReadCollectionRenderer data={data} columnsConfig={columnsConfig} />
    </main>
  );
}
