
import { PermissionGuard } from "@/_core/ui/components/permissonGuard/permissionGuard";

import { RecoveryFormServer } from "./components/recoveryFormServer";
import LogoModalWrapper from "../components/logoModalWrapper";

export default function Page() {
  return (
    <LogoModalWrapper>
        <PermissionGuard entity="token" action="recovery" fallback={<></>}>
            <RecoveryFormServer/>
        </PermissionGuard>
    </LogoModalWrapper>
  );
}
