"use server";

import { getSubmitActionFor } from "@/_lib/data/model/action/actionFactory";

import { metaLoginBody } from "../meta/metaLoginBody";
import { RecoveryFormClient } from "./recoveryFormClient";

import { Box, Typography } from "@mui/material";

const ENTITY = "token";
const ACTION = "recovery";

export async function RecoveryFormServer() {
  const serverAction = await getSubmitActionFor(ENTITY, ACTION);

  return (
    <>
      <Box className="mt-6 md:mt-12 lg:mt-14 md:mx-2 lg:mx-0">
        <Typography
          sx={{ typography: { xs: "body2", md: "body2xl", lg: "body1xl" } }}
        >
          Recuperar contraseña
        </Typography>
      </Box>
      <RecoveryFormClient
        entityName={ENTITY}
        actionName={ACTION}
        meta={metaLoginBody}
        action={serverAction}
      />
    </>
  );
}
