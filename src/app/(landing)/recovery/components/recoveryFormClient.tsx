"use client";

import Link from "next/link";

import { useState } from "react";

import { ClientFormWrapper } from "@/_core/ui/components/forms/clientFormWrapper";
import { FieldMeta } from "@/_core/ui/components/forms/types";
import { InferSchema, Entity<PERSON><PERSON><PERSON>, ActionKeys } from "@/_lib/data/model/schema";

import { Box } from "@mui/material";
import { FormSubmitButton } from "@/_core/ui/components/forms/formSubmitButton";
import { FormCancelButton } from "@/_core/ui/components/forms/formCancelButton";
import { ServerActionParams } from "@/_lib/data/model/action/actionFactory";

type RecoveryFormClientProps<E extends EntityKeys, A extends ActionKeys<E>> = {
  entityName: E;
  actionName: A;
  meta: Record<keyof InferSchema<E, A>, FieldMeta>;
  action: (params: ServerActionParams) => Promise<unknown>;
};

export function RecoveryFormClient<
  E extends EntityKeys,
  A extends ActionKeys<E>
>({ entityName, actionName, meta, action }: RecoveryFormClientProps<E, A>) {
  const [email, setEmail] = useState("");

  const interceptedAction = async (params: ServerActionParams) => {
    const response = (await action({body: params.body})) as { email: string };
    setEmail(response?.email);
  };

  if (email) {
    return (
      <Box className="mt-6 md:mt-10 lg:mt-12 md:mx-2 lg:mx-0">
        <Box className="text-center">
          <h2 className="text-lg font-semibold">Revisá tu correo</h2>
          <p>Enviamos el correo a la casilla</p>
          <p>{email}</p>
          <Link href="/login" className="mt-4">
            <div className="py-2">
              <FormCancelButton label="Cancelar" />
            </div>
          </Link>
        </Box>
      </Box>
    );
  }

  return (
    <Box className="mt-4 md:mt-8 lg:mt-10 md:mx-2 lg:mx-0">
      <ClientFormWrapper
        entityName={entityName}
        actionName={actionName}
        meta={meta}
        action={interceptedAction}
      >
        <Box
          className="
            items-center 
            md:mt-1 lg:mt-5
            mx-1 lg:mx-0"
        >
          <Box className="mt-12 md:mt-16 lg:mt-20 ">
            <div className="py-2">
              <FormSubmitButton label="Confirmar" />
            </div>
          </Box>
          <Box>
            <Link href={"/login"}>
              <div className="md:py-2">
                <FormCancelButton label="Cancelar" />
              </div>
            </Link>
          </Box>
        </Box>
      </ClientFormWrapper>
    </Box>
  );
}
