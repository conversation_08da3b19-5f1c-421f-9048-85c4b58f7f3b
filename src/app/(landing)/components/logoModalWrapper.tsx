import Image from "next/image";

import { Box, Paper, Typography } from "@mui/material";

export default function LogoModalWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div
      className="
        flex
        justify-center items-center
        h-screen"
    >
      <Paper
        elevation={10}
        className="
            lg:w-115 md:w-95 w-75
            lg:px-12 md:px-9 px-8
            pt-5 md:pt-20 lg:pt-19 xl:pt-14
            pb-5 lg:pb-17 xl:pb-14
            lg:translate-y-0 md:translate-y-[-3rem] translate-y-[-1rem]
            rounded-2xl shadow-md drop-shadow-xl"
      >
        <Box
          className="
              flex flex-col items-center"
        >
          <Image
            className="block md:hidden"
            src="/bqn_logo.svg"
            alt="LogoXS"
            width={54}
            height={54}
          />
          <Image
            className="hidden md:block lg:hidden"
            src="/bqn_logo.svg"
            alt="LogoMD"
            width={69}
            height={69}
          />
          <Image
            className="hidden lg:block"
            src="/bqn_logo.svg"
            alt="LogoLG"
            width={84}
            height={84}
          />
          <Typography
            sx={{ typography: { xs: "h6", md: "h6xl", lg: "h5xl" } }}
            className="
                lg:mt-5 md:mt-3 mt-4
                font-bold text-center"
            color="primary"
          >
            Colegio de Bioquímicos NQN
          </Typography>
        </Box>
        {children}
      </Paper>
    </div>
  );
}
