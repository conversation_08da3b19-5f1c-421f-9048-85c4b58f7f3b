import Image from "next/image";

export default function LandingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div>
      <main className="relative min-h-screen">
        <Image
          src="/landing_bg_375x812.svg"
          alt="XS"
          fill
          className="object-cover block md:hidden pointer-events-none select-none"
          priority
        />
        <Image
          src="/landing_bg_834x1194.svg"
          alt="MD"
          fill
          className="object-cover hidden md:block lg:hidden pointer-events-none select-none"
          loading="lazy"
        />
        <Image
          src="/landing_bg_1440x1024.svg"
          alt="LG"
          fill
          className="object-cover hidden lg:block 2xl:hidden pointer-events-none select-none"
          loading="lazy"
        />
        <Image
          src="/landing_bg_1920x1080.svg"
          alt="2XL"
          fill
          className="object-cover hidden 2xl:block pointer-events-none select-none"
          loading="lazy"
        />

        <div className="relative z-10">{children}</div>
      </main>
    </div>
  );
}
