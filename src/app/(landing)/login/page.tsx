"use server";

import { PermissionGuard } from "@/_core/ui/components/permissonGuard/permissionGuard";
import LogoModalWrapper from "../components/logoModalWrapper";
import { LoginFormServer } from "./components/loginFormServer";

import { Box, Typography } from "@mui/material";


export default async function Page() {
  return (
    <LogoModalWrapper>
        <Box>
          <Typography
            sx={{ typography: { xs: "body2", md: "body2xl", lg: "body1xl" } }}
            className="
                mt-[0.1rem] md:mt-[0.4rem] lg:mt-[0.3rem]
                font-semibold text-center"
            color="primary"
          >
            Iniciar se<PERSON><PERSON>
          </Typography>
        </Box>
        <Box>
          {/* Formulario */}
          <PermissionGuard entity="token" action="login" fallback={<></>}>
            <LoginFormServer />
          </PermissionGuard>
        </Box>
    </LogoModalWrapper>
  );
}
