import "@/_ui/globals.css";
import { inter } from "@/_ui/fonts/fonts";

import { AppRouterCacheProvider } from "@mui/material-nextjs/v15-appRouter";

import { ThemeRegistry } from "@/_core/ui/components/themeRegistry/themeRegistry";
import { ReactQueryProvider } from "@/_core/lib/provider/tanstackQuery/components/reactQueryProvider";
import { Notifiable } from "@/_core/ui/components/notifiable/notifiable";

export const metadata = {
  title: {
    default: "Colegio de Bioquímicos NQN",
    template: "%s | Colegio de Bioquímicos NQN",
  },
  description: "Colegio de Bioquímicos NQN - Sistema Integral de Gestion de bioquímicos de la provincia de Neuquén",
  openGraph: {
    title: "Colegio de Bioquímicos NQN - Sistema Integral de Gestion de bioquímicos de la provincia de Neuquén",
  },
  twitter: {
    title: "Colegio de Bioquímicos NQN - Sistema Integral de Gestion de bioquímicos de la provincia de Neuquén",
  },
  icons: {
    icon: "/favicon.ico?v=1",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es">
      <body className={`${inter.variable}`}>
        <AppRouterCacheProvider options={{ enableCssLayer: true }}>
          <ThemeRegistry>
            <ReactQueryProvider>
              <Notifiable>{children}</Notifiable>
            </ReactQueryProvider>
          </ThemeRegistry>
        </AppRouterCacheProvider>
      </body>
    </html>
  );
}
