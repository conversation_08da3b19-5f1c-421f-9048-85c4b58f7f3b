 "server-only";

import { validateApiInput } from "@/_core/lib/service/validationService";
import { CollectionHttpRequestBuilder, IndexHttpRequestBuilder } from "@/_core/lib/data/action/builder";
import { LocalidadesInterface } from "@/_lib/data/model/interface/";
import { getEntityConfig } from "@/_lib/utils/entities";

export async function submitLocalidadesIndex() {
  "use server";

  const { meta } = getEntityConfig("localidades", "index"); 

  const builder = new IndexHttpRequestBuilder(meta.url)
    .withValidation(LocalidadesInterface.indexLocalidadesModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        LocalidadesInterface.indexLocalidadesModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}

export async function submitLocalidadesCollection() {
  "use server";

  const { meta } = getEntityConfig("localidades", "collection");

  const builder = new CollectionHttpRequestBuilder(meta.url)

  const response = await builder.run();
  return response.data;
}
