'server-only';

import { validateApiInput } from "@/_core/lib/service/validationService";
import { CollectionHttpRequestBuilder, CreateHttpRequestBuilder } from "@/_core/lib/data/action/builder";
import { AsociadosInterface } from "@/_lib/data/model/interface/";

import { getEntityConfig } from "@/_lib/utils/entities";

import { ServerActionParams } from "../actionFactory";
import { BadRequestError } from "@/_core/lib/context/error";

export async function submitAsociadosCollection() {
  "use server";

  const { meta } = getEntityConfig("asociados", "collection");

  const builder = new CollectionHttpRequestBuilder(meta.url)
    .withValidation(AsociadosInterface.collectionAsociadosModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        AsociadosInterface.collectionAsociadosModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}

export async function submitAsociadosCreate( params: ServerActionParams ) {
  "use server";
  const { meta } = getEntityConfig("asociados", "create");

  const { body } = params;
  if (!body) {
    throw new BadRequestError( "La petición no contiene un body.");
  }

  // Función para reconstruir objetos anidados desde FormData aplanado
  const reconstructNestedObject = (formData: FormData) => {
    const result: any = {};

    for (const [key, value] of formData.entries()) {
      const keys = key.split('.');
      let current = result;

      // Navegar/crear la estructura anidada
      for (let i = 0; i < keys.length - 1; i++) {
        const currentKey = keys[i];
        if (!(currentKey in current)) {
          current[currentKey] = {};
        }
        current = current[currentKey];
      }

      // Asignar el valor final
      const finalKey = keys[keys.length - 1];
      let finalValue: any = value;

      // Convertir tipos apropiados
      if (finalKey.toLowerCase().includes('id') && value && !isNaN(Number(value))) {
        finalValue = Number(value);
      } else if (value === 'true') {
        finalValue = true;
      } else if (value === 'false') {
        finalValue = false;
      } else if (!isNaN(Number(value)) && value !== '') {
        // Solo convertir a número si es claramente numérico
        const numValue = Number(value);
        if (Number.isInteger(numValue)) {
          finalValue = numValue;
        }
      }

      current[finalKey] = finalValue;
    }

    return result;
  };

  const ObjectBody = reconstructNestedObject(body);
  console.log("ObjectBody reconstructed", JSON.stringify(ObjectBody, null, 2));

  const builder = new CreateHttpRequestBuilder(meta.url)
    .setBody(ObjectBody)
    .withValidation(AsociadosInterface.createAsociadosModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        AsociadosInterface.createAsociadosModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}


