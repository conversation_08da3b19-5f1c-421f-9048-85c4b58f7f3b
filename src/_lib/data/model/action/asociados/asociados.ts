'server-only';

import { validateApiInput } from "@/_core/lib/service/validationService";
import { CollectionHttpRequestBuilder, CreateHttpRequestBuilder } from "@/_core/lib/data/action/builder";
import { AsociadosInterface } from "@/_lib/data/model/interface/";

import { getEntityConfig } from "@/_lib/utils/entities";

import { ServerActionParams, processFormDataToObject } from "../actionFactory";
import { BadRequestError } from "@/_core/lib/context/error";

export async function submitAsociadosCollection() {
  "use server";

  const { meta } = getEntityConfig("asociados", "collection");

  const builder = new CollectionHttpRequestBuilder(meta.url)
    .withValidation(AsociadosInterface.collectionAsociadosModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        AsociadosInterface.collectionAsociadosModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}

export async function submitAsociadosCreate( params: ServerActionParams ) {
  "use server";
  const { meta } = getEntityConfig("asociados", "create");

  const { body } = params;
  if (!body) {
    throw new BadRequestError( "La petición no contiene un body.");
  }

  // Usar la función utilitaria genérica para procesar FormData
  const ObjectBody = processFormDataToObject(body);
  console.log("ObjectBody reconstructed", JSON.stringify(ObjectBody, null, 2));

  const builder = new CreateHttpRequestBuilder(meta.url)
    .setBody(ObjectBody)
    .withValidation(AsociadosInterface.createAsociadosModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        AsociadosInterface.createAsociadosModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}


