"use server";

import * as tokenActions from "./token/token";
import * as asociadosActions from "./asociados/asociados";
import * as localidadesActions from "./localidades/localidades";

import { ENTITIES } from "@/_lib/config/entities";
import { LoadStaticDataError } from "@/_core/lib/context/error";

/**
 * Tipo de función que representa una acción del servidor.
 * Recibe un FormData y retorna una promesa con el resultado de la acción.
 */
type ServerAction = (params : ServerActionParams) => Promise<unknown>;

export type ServerActionParams = {
  params?: Record<string, string | number | boolean>;
  query?: Record<string, string | number | boolean>;
  body?: FormData;
}

/**
 * Tipos de claves para las entidades y acciones.
 */
type EntityKey = keyof typeof ENTITIES;
type ActionKey<E extends EntityKey> = keyof typeof ENTITIES[E];

/**
 * Mó<PERSON>lo que contiene las acciones del servidor para cada entidad.
 */
const modules: Record<EntityKey, Record<string, ServerAction>> = {
  token: tokenActions,
  asociados: asociadosActions,
  localidades: localidadesActions,
};

function capitalizeFirstLetter(s: string) {
  return s.charAt(0).toUpperCase() + s.slice(1);
}

/**
 * Retorna la server action correspondiente según entidad y acción.
 * @param entity 
 * @param action 
 * @returns ServerAction
 * @throws LoadStaticDataError si no se encuentra la acción o el módulo.
 */
export async function getSubmitActionFor<E extends EntityKey, A extends ActionKey<E>>(
  entity: E,
  action: A
): Promise<ServerAction> {
  // validaciones???

  const methodName = `submit${capitalizeFirstLetter(entity)}${capitalizeFirstLetter(action as string)}`;

  const moduleExports = modules[entity];
  if (!moduleExports) {
    throw new LoadStaticDataError(entity);
  }

  const actionFn = moduleExports[methodName as keyof typeof moduleExports];

  if (typeof actionFn !== "function") {
    throw new LoadStaticDataError(`${entity}.${String(action)}`);
  }

  return actionFn as ServerAction;
}

/**
 * Recibe una entidad, una acción y los datos del formulario, y ejecuta la acción correspondiente.
 * @param entity 
 * @param action 
 * @param params 
 * @returns 
 */
export async function runFormAction<E extends EntityKey, A extends ActionKey<E>>(
  entity: E,
  action: A,
  params: ServerActionParams
) {
  const serverAction = await getSubmitActionFor(entity, action);
  return serverAction(params);
}
