 import { ApiModelCollectionBuilder } from "@/_core/lib/data/interface/builder";
import { ENTITIES } from "@/_lib/config/entities";
import { getEntityKey, getActionKeys } from "@/_lib/utils/entities";
import { localidadesSchemas } from "../schema/localidades";

const { localidades } = ENTITIES;

// Nombre de la entidad
export const NAME = getEntityKey(localidades) as string;

// Acciones disponibles en la entidad
export const ACTION_NAMES = getActionKeys(localidades) as (keyof typeof localidadesSchemas)[];

// Modelos generados
export const indexLocalidadesModel = new ApiModelCollectionBuilder(ACTION_NAMES[0])
  .setResponseItemsSchema(localidadesSchemas.index.response)
  .build();

export const collectionLocalidadesModel = new ApiModelCollectionBuilder(ACTION_NAMES[1])
  .setFilters(localidadesSchemas.collection.filters)
  .setResponseItemsSchema(localidadesSchemas.collection.response)
  .build();