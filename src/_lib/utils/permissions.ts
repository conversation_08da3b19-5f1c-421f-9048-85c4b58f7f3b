// lib/permissions.ts

import { EntityKeys, ActionKeys, getEntityConfig } from "./entities";
import { PermissionNotFoundError } from "@/_core/lib/context/error/internalServerError";
import { Context, EntityActionConfig } from "@/_lib/config/types";

export function hasAccess<E extends EntityKeys, A extends ActionKeys<E>>(
  entity: E,
  action: A,
  context?: Context
): boolean {
  const config = getEntityConfig(entity, action);
  const allowedContexts = (config as EntityActionConfig).permissions;

  if (!allowedContexts) {
    throw new PermissionNotFoundError(entity, String(action));
  }

  return allowedContexts.includes(context ?? "public") || context === "admin";
}