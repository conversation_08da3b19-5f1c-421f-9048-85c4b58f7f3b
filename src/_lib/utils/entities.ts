import { ENTITIES } from "@/_lib/config/entities/";
import { LoadStaticDataError } from "@/_core/lib/context/error";

export type EntityKeys = keyof typeof ENTITIES;
export type ActionKeys<E extends EntityKeys> = keyof (typeof ENTITIES)[E];

export function getEntityKey<T extends keyof typeof ENTITIES>(
  target: (typeof ENTITIES)[T]
): T | undefined {
  return (Object.entries(ENTITIES) as [T, unknown][]).find(
    ([, value]) => value === target
  )?.[0];
}

export function getActionKeys<T extends keyof typeof ENTITIES>(
  entity: (typeof ENTITIES)[T]
): (keyof (typeof ENTITIES)[T])[] {
  return Object.keys(entity) as (keyof (typeof ENTITIES)[T])[];
}

export function getEntityConfig<
  E extends keyof typeof ENTITIES,
  A extends keyof (typeof ENTITIES)[E]
>(
  entityName: E,
  actionName: A
): (typeof ENTITIES)[E][A] {
  const entity = ENTITIES[entityName];
  if (!entity) throw new LoadStaticDataError(`${entityName}`);

  const action = entity[actionName];
  if (!action) throw new LoadStaticDataError(`${entityName}.${String(actionName)}`);

  return action;
}
