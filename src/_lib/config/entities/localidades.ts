import { EntityActions } from "../types";

export const LOCALIDADES_CONFIG: EntityActions = {
  collection: {
    description: "Listado de localidades",
    meta: {
      url: "/localidades",
      method: "GET",
      revalidates: {
        keys: [],
      }
    },
    permissions: ["public"],
  },
  index: {
    description: "Detalle de localidad",
    meta: {
      url: "/localidades/:id",
      method: "GET",
      revalidates: {
        keys: [],
      }
    },
    permissions: ["public"],
  },
} as const;
