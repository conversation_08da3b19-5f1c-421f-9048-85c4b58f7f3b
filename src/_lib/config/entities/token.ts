import { EntityActions } from "../types";

export const TOKEN_CONFIG: EntityActions = {
  login: {
    description: "Login",
    meta: {
      url: "/auth/login",
      method: "POST",
      revalidates: { keys: [] }
    },
    permissions: ["public"],
  },
  refresh: {
    description: "Refresh token",
    meta: {
      url: "/auth/refresh",
      method: "POST",
      revalidates: { keys: [] }
    },
    permissions: ["public"],
  },
  logout: {
    description: "Logout",
    meta: {
      url: "",
      method: "POST",
      revalidates: { keys: [] }
    },
    permissions: ["public"],
  },
  recovery: {
    description: "Recuperación de contraseña",
    meta: {
      url: "/usuarios/forgot_password",
      method: "POST",
      revalidates: { keys: [] }
    },
    permissions: ["public"],
  },
} as const;
