import { Method } from "axios";

export type Context =
  | "public"
  | "colegio"
  | "asociado"
  | "laboratorio"
  | "sucursal"
  | "obra_social"
  | "admin";

export type EntityActionConfig = {
  description: string;
  meta: {
    url: string;
    method: Method;
    revalidates: {
      keys: string[];
      time?: number;
    };
  };
  permissions: Context[];
};

export type EntityActions = {
  [actionName: string]: EntityActionConfig;
};

export type ENTITIES_MAP = Record<string, EntityActions>;
