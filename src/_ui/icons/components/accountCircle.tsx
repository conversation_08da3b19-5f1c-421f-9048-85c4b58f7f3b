import * as React from "react";
import IconBase from "@/_core/ui/components/iconBase/iconBase";

const AccountCircle = React.forwardRef<
  SVGSVGElement,
  React.ComponentProps<typeof IconBase>
>((props, ref) => (
  <IconBase
    ref={ref}
    viewBox="0 0 30 30"
    fill={props.color ?? "#69798B"}
    stroke="none"
    {...props}
  >
    <path d="M6.28717 22.2252C7.49134 21.3043 8.83717 20.5783 10.3247 20.047C11.8122 19.5158 13.3705 19.2502 14.9997 19.2502C16.6288 19.2502 18.1872 19.5158 19.6747 20.047C21.1622 20.5783 22.508 21.3043 23.7122 22.2252C24.5386 21.2571 25.182 20.1592 25.6424 18.9314C26.1028 17.7036 26.333 16.3932 26.333 15.0002C26.333 11.8599 25.2292 9.18593 23.0215 6.97829C20.8139 4.77065 18.14 3.66683 14.9997 3.66683C11.8594 3.66683 9.18544 4.77065 6.9778 6.97829C4.77016 9.18593 3.66634 11.8599 3.66634 15.0002C3.66634 16.3932 3.89655 17.7036 4.35697 18.9314C4.81738 20.1592 5.46079 21.2571 6.28717 22.2252ZM14.9997 16.4168C13.6066 16.4168 12.432 15.9387 11.4757 14.9825C10.5195 14.0262 10.0413 12.8516 10.0413 11.4585C10.0413 10.0654 10.5195 8.89079 11.4757 7.93454C12.432 6.97829 13.6066 6.50016 14.9997 6.50016C16.3927 6.50016 17.5674 6.97829 18.5236 7.93454C19.4799 8.89079 19.958 10.0654 19.958 11.4585C19.958 12.8516 19.4799 14.0262 18.5236 14.9825C17.5674 15.9387 16.3927 16.4168 14.9997 16.4168ZM14.9997 29.1668C13.04 29.1668 11.1983 28.795 9.47467 28.0512C7.75106 27.3075 6.25176 26.2981 4.97676 25.0231C3.70176 23.7481 2.69238 22.2488 1.94863 20.5252C1.20488 18.8016 0.833008 16.9599 0.833008 15.0002C0.833008 13.0404 1.20488 11.1988 1.94863 9.47516C2.69238 7.75155 3.70176 6.25225 4.97676 4.97725C6.25176 3.70225 7.75106 2.69287 9.47467 1.94912C11.1983 1.20537 13.04 0.833496 14.9997 0.833496C16.9594 0.833496 18.8011 1.20537 20.5247 1.94912C22.2483 2.69287 23.7476 3.70225 25.0226 4.97725C26.2976 6.25225 27.307 7.75155 28.0507 9.47516C28.7945 11.1988 29.1663 13.0404 29.1663 15.0002C29.1663 16.9599 28.7945 18.8016 28.0507 20.5252C27.307 22.2488 26.2976 23.7481 25.0226 25.0231C23.7476 26.2981 22.2483 27.3075 20.5247 28.0512C18.8011 28.795 16.9594 29.1668 14.9997 29.1668Z" />
  </IconBase>
));

AccountCircle.displayName = "AccountCircle";

export default AccountCircle;
