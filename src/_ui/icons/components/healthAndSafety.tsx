import * as React from "react";
import IconBase from "@/_core/ui/components/iconBase/iconBase";

const HealthAndSafety = React.forwardRef<
  SVGSVGElement,
  React.SVGProps<SVGSVGElement>
>((props, ref) => (
  <IconBase
    ref={ref}
    {...props}
    viewBox="0 -960 960 960"
    fill="currentColor"
    stroke="none"
  >
    <path d="M420-340h120v-100h100v-120H540v-100H420v100H320v120h100v100Zm60 260q-139-35-229.5-159.5T160-516v-244l320-120 320 120v244q0 152-90.5 276.5T480-80Zm0-84q104-33 172-132t68-220v-189l-240-90-240 90v189q0 121 68 220t172 132Zm0-316Z" />
  </IconBase>
));

HealthAndSafety.displayName = "HealthAndSafety";

export default HealthAndSafety;
