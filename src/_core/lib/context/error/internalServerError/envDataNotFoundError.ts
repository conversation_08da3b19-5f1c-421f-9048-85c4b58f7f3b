import { DefaultError, DefaultErrorOptions } from "../default";

export class EnvDataNotFoundError extends DefaultError {
  public readonly env_data_name: string;

  constructor(env_data_name: string, options: DefaultErrorOptions = {}) {
    super(`Información de entorno no encontrada: ${env_data_name}`, {
      code: options.code || '500_02_ENV_DATA_NOT_FOUND',
      statusCode: options.statusCode || 500,
      details: options.details,
    });

    this.name = 'EnvDataNotFound';
    this.env_data_name = env_data_name;

    // Mantener el stack trace.
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}
