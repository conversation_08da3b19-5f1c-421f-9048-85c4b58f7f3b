import { DefaultError, DefaultErrorOptions } from "../default";

export class PermissionNotFoundError extends DefaultError {
  public readonly action: string;
  public readonly entity: string;

  constructor(entity: string, action: string, options: DefaultErrorOptions = {}) {
    super( `Permiso no encontrado para la entidad ${entity} y la acción ${action}`, {
      code: options.code || '500_03_PERMISSION_NOT_FOUND',
      statusCode: options.statusCode || 500,
      details: options.details,
    });

    this.name = 'PermissionNotFoundError';
    this.entity = entity;
    this.action = action;

    // Mantener el stack trace.
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}
