export interface DefaultErrorOptions extends ErrorOptions {
  code?: string;
  statusCode?: number;
  details?: unknown;
}

export class DefaultError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly details?: unknown;

  constructor(message: string, options: DefaultErrorOptions = {}) {
    super(message, options);
    this.name = new.target.name;
    this.code = options.code ?? "500_01_DEFAULT";
    this.statusCode = options.statusCode ?? 500;
    this.details = options.details;

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  toString(): string {
    const codePart = this.code ? `[${this.code}]` : '';
    const messagePart = this.message || '';
    const detailsPart = this.details
      ? ` – ${typeof this.details === 'object'
          ? JSON.stringify(this.details)
          : String(this.details)}`
      : '';

    return `${codePart} ${messagePart}${detailsPart}`.trim();
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      status: this.statusCode,
      code: this.code,
      details: this.details,
    };
  }
}
