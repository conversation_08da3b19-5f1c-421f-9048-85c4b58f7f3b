import { AxiosError } from "axios";
import { DefaultError } from "./default";

export class FetchingError extends DefaultError {
  constructor(error: AxiosError) {
    const statusCode = error.response?.status ?? 500;
    const message =
      getErrorMessage(error) ?? "Error al obtener los datos del servidor.";
    const details = error.response?.data ?? error.toJSON?.() ?? error;

    super(message, {
      statusCode,
      code: `${statusCode}_99_FETCHING_ERROR`,
      details,
    });

    this.name = "FetchingError";
  }
}

function getErrorMessage(error: AxiosError): string | undefined {
  const data = error.response?.data;

  if (typeof data === "string") return data;

  if (isRecord(data)) {
    if (typeof data.message === "string") return data.message;
    if (typeof data.error === "string") return data.error;
  }

  return error.message;
}

function isRecord(value: unknown): value is Record<string, unknown> {
  return typeof value === "object" && value !== null;
}
