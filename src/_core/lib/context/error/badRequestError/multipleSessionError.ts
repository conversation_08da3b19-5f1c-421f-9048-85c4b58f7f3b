import { DefaultError, DefaultErrorOptions } from "../default";

export class MultipleSessionError extends DefaultError {

  constructor(options: DefaultErrorOptions = {}) {
    super(`No es posible tener múltiples sesiones activas`, {
      code: options.code || '403_02_FORBIDDEN',
      statusCode: options.statusCode || 403,
      details: options.details,
    });

    this.name = 'MultipleSessionError';

    // Mantener el stack trace.
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}
