 import { DefaultError, DefaultErrorOptions } from "../default";

export class BadRequestError extends DefaultError {

  constructor( message: string ,options: DefaultErrorOptions = {}) {
    super(`Bad Request: ${message}`, {
      code: options.code || '400_01_BAD_REQUEST',
      statusCode: options.statusCode || 400,
      details: options.details,
    });

    this.name = 'BadRequestError';

    // Mantener el stack trace.
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}
