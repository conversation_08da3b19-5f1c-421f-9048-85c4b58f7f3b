import { NextRequest, NextResponse } from "next/server";
import { DefaultError } from "@/_core/lib/context/error/default";

type RouteHandler = (req: NextRequest) => Promise<NextResponse>;

export function with<PERSON>rror<PERSON>andler(handler: RouteHand<PERSON>): RouteHandler {
  return async function (req: NextRequest): Promise<NextResponse> {
    try {
      return await handler(req);
    } catch (error: unknown) {
      if (error instanceof DefaultError) {
        return NextResponse.json(error.toJSON(), {
          status: error.statusCode,
        });
      }

      // fallback
      return NextResponse.json(
        {
          name: "UnexpectedError",
          message: "Unexpected error occurred",
        },
        { status: 500 }
      );
    }
  };
}
