import { AxiosResponse } from "axios";
import { BaseHttpRequest } from "../base/baseHttpRequest";

export type Hook = () => Promise<void> | void;

export type AfterBuildHook<TRequest, TResponse> = (
  request: BaseHttpRequest<TRequest, TResponse>
) => Promise<void> | void;

export type AfterExecuteHook<TResponse> = (
  response: AxiosResponse<TResponse>
) => Promise<void> | void;

export class HttpRequestBuilderHooks<TRequest, TResponse> {
  private beforeBuildHooks: Hook[] = [];
  private afterBuildHooks: AfterBuildHook<TRequest, TResponse>[] = [];
  private beforeExecuteHooks: Hook[] = [];
  private afterExecuteHooks: AfterExecuteHook<TResponse>[] = [];

  addBeforeBuild(hook: Hook): this {
    this.beforeBuildHooks.push(hook);
    return this;
  }

  addAfterBuild(hook: AfterBuildHook<TRequest, TResponse>): this {
    this.afterBuildHooks.push(hook);
    return this;
  }

  addBeforeExecute(hook: Hook): this {
    this.beforeExecuteHooks.push(hook);
    return this;
  }

  addAfterExecute(hook: AfterExecuteHook<TResponse>): this {
    this.afterExecuteHooks.push(hook);
    return this;
  }

  async runBeforeBuild(): Promise<void> {
    for (const hook of this.beforeBuildHooks) {
      await hook();
    }
  }

  async runAfterBuild(request: BaseHttpRequest<TRequest, TResponse>): Promise<void> {
    for (const hook of this.afterBuildHooks) {
      await hook(request);
    }
  }

  async runBeforeExecute(): Promise<void> {
    for (const hook of this.beforeExecuteHooks) {
      await hook();
    }
  }

  async runAfterExecute(response: AxiosResponse<TResponse>): Promise<void> {
    for (const hook of this.afterExecuteHooks) {
      await hook(response);
    }
  }
}
