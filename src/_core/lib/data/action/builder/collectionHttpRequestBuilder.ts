import { AxiosResponse } from "axios";
import { BaseHttpRequestBuilder } from "./baseHttpRequestBuilder";
import { BaseApiModel } from "@/_core/lib/data/interface/base/baseApiModel";
import { validateApiInput } from "@/_core/lib/service/validationService";
import { toQueryParams } from "@/_core/utils/typeConversion";

export class CollectionHttpRequestBuilder<
  TResponse = unknown
> extends BaseHttpRequestBuilder<undefined, TResponse> {
  constructor( url: string ) {
    super(url, "GET");
  }

  /**
   * Inyecta validación de filtros
   */
  withValidation(model: BaseApiModel, fullMask?: boolean): this {
    this.addBeforeBuild(() => {
      const result = validateApiInput(model, "filters", this["config"].params || {});
      if (fullMask) {
        this.setQueryParams(toQueryParams(result));
      }
    });

    this.addAfterExecute((response: AxiosResponse<TResponse>) => {
      const result = validateApiInput(model, "response", response.data);
      if (fullMask) {
        response.data = result as TResponse;
      }
    });

    return this;
  }
}
