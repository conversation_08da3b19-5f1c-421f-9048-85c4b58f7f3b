import { BaseSchemaModel, SchemaType } from '../base/baseSchemaModel';

export class SchemaModelFactory {
  private _schemas: Map<string, BaseSchemaModel>;

  constructor() {
    this._schemas = new Map();
  }

  register(name: string, schema: SchemaType): void {
    if (this._schemas.has(name)) {
      throw new Error(`Schema "${name}" ya está registrado.`);
    }
    this._schemas.set(name, new BaseSchemaModel(name, schema));
  }

  get(name: string): BaseSchemaModel {
    const schema = this._schemas.get(name);
    if (!schema) {
      throw new Error(`Schema "${name}" no está registrado.`);
    }
    return schema;
  }

  has(name: string): boolean {
    return this._schemas.has(name);
  }

  list(): string[] {
    return [...this._schemas.keys()];
  }

  clear(): void {
    this._schemas.clear();
  }

  export(): Map<string, BaseSchemaModel> {
    return this._schemas;
  }
}
