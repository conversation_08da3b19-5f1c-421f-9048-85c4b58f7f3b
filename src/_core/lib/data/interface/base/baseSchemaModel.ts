import { z, ZodObject, ZodRawShape } from 'zod';

export type SchemaType = ZodObject<ZodRawShape>

export class BaseSchemaModel {
  
  readonly _name: string;

  readonly schema: SchemaType;

  constructor(_name: string, schema: SchemaType = z.object({})) {
    this._name = _name;
    this.schema = schema;
  }

  validate(input: unknown) {
    return this.schema.safeParse(input);
  }

  parse(input: unknown) {
    return this.schema.parse(input);
  }
}