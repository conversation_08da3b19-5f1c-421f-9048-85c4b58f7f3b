// baseApiModel.ts
import { z } from 'zod';
import { SchemaModelFactory } from '../factory/schemaModelFactory';
import { BaseSchemaModel, SchemaType } from './baseSchemaModel';

export type ApiSchemaModel = {
  params?: SchemaType;
  filters?: SchemaType;
  body?: SchemaType;
  response?: SchemaType;
};

export type SchemaKind = keyof ApiSchemaModel;

export class BaseApiModel {
  readonly _name: string;
  private schemas: Map<string, BaseSchemaModel>;

  constructor(
    _name: string,
    schemas: Partial<ApiSchemaModel>,
  ) {
    this._name = _name;
    this.schemas = new Map();
    
    this.registerSchemas(schemas);
  }

  registerSchemas(schemas: Partial<ApiSchemaModel>) {
    const factory = new SchemaModelFactory();
    for (const [key, schema] of Object.entries(schemas)) {
      const kind = key as Schema<PERSON>ind;
      factory.register(`${this._name}.${kind}`, schema ?? z.object({}));
    }
    this.schemas = factory.export();
  }

  getSchema(kind: SchemaKind) {
    const fullName = `${this._name}.${kind}`;
    const schema = this.schemas.get(fullName);
    if (!schema) {
      throw new Error(`Schema "${fullName}" no encontrado.`);
    }
    return schema;
  }

  validate(kind: SchemaKind, input: unknown) {
    const fullName = `${this._name}.${kind}`;
    const schemaModel = this.schemas.get(fullName);
    return schemaModel?.validate(input) || { success: false, error: new Error(`Schema "${fullName}" no encontrado.`) };
  }
}
