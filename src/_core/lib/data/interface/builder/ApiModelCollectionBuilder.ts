/**
 * Builder para endpoints de colección (GET /items).
 * Incluye paginación por defecto y permite configurar el tipo de datos.
 */
import { z, ZodTypeAny, ZodObject, ZodRawShape } from "zod";
import { ApiModelBaseBuilder } from "./ApiModelBaseBuilder";

export const paginationSchema = z
  .object({
    itemsPerPage: z.number().int().min(0).default(0).optional(),
    totalItems: z.number().int().min(0).default(0).optional(),
    currentPage: z.number().int().min(0).default(0).optional(),
    totalPages: z.number().int().min(0).default(0).optional(),
  })
  .optional();

export const queryPaginationSchema = z.object({
  page: z.number().int().min(1).default(1).optional().nullable(),
  limit: z.number().int().min(1).max(30).default(30).optional().nullable(),
});

export class ApiModelCollectionBuilder extends ApiModelBaseBuilder {
  constructor(_name: string = "collection") {
    super(_name);
    this.setFilters(queryPaginationSchema);
    this.setResponse(
      z.object({
        data: z.array(z.unknown()),
        meta: paginationSchema,
      })
    );
  }

  extendResponseSchema<T extends ZodRawShape>(schema: ZodObject<T>) {
    const oldResponse = this.schemas.response!;

    const merged = oldResponse ? oldResponse.merge(schema) : schema;

    this.setResponse(merged);
    return this;
  }

  extendFiltersSchema<T extends ZodRawShape>(schema: ZodObject<T>) {
    const oldFilters = this.schemas.filters!;

    const merged = oldFilters ? oldFilters.merge(schema) : schema;

    this.setFilters(merged);
    return this;
  }

  setResponseItemsSchema(schema: ZodTypeAny) {
    const oldResponse = this.schemas.response!;
    this.setResponse(
      oldResponse.extend({
        data: z.array(schema),
      })
    );
    return this;
  }

  setResponsePaginationSchema(schema: ZodTypeAny) {
    const oldResponse = this.schemas.response!;
    this.setResponse(
      oldResponse.extend({
        meta: schema,
      })
    );
    return this;
  }
}
