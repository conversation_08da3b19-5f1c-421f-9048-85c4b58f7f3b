/**
 * Builder base para construir modelos API con esquemas Zod.
 * Permite definir params, filters, body y response de forma encadenada.
 * Utilizado como base por los builders especializados.
 */
import { ZodObject, ZodRawShape } from "zod";
import { BaseApiModel, ApiSchemaModel } from "../base/baseApiModel";

export class ApiModelBaseBuilder {
  readonly _name: string;

  protected schemas: Partial<ApiSchemaModel> = {};

  constructor(_name: string) {
    this._name = _name;
  }

  setParams<T extends ZodRawShape>(schema: ZodObject<T>) {
    this.schemas.params = schema;
    return this;
  }

  setFilters<T extends ZodRawShape>(schema: ZodObject<T>) {
    this.schemas.filters = schema;
    return this;
  }

  setBody<T extends ZodRawShape>(schema: ZodObject<T>) {
    this.schemas.body = schema;
    return this;
  }

  setResponse<T extends ZodRawShape>(schema: ZodObject<T>) {
    this.schemas.response = schema;
    return this;
  }

  build() {
    return new BaseApiModel(this._name, this.schemas);
  }
}
