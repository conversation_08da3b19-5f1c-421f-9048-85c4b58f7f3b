/**
 * Builder para endpoints de eliminación (DELETE /items/:id).
 * Define ID como parámetro y éxito como respuesta.
 */
import { z, ZodObject, ZodRawShape } from "zod";
import { ApiModelBaseBuilder } from "./ApiModelBaseBuilder";

export class ApiModelDeleteBuilder extends ApiModelBaseBuilder {
  constructor(_name: string = 'delete') {
    super(_name);
    this.setParams(
      z.object({
        id: z.string().uuid(),
      })
    );
    this.setResponse(
      z.object({
        success: z.boolean(),
      })
    );
  }

  extendResponseSchema<T extends ZodRawShape>(schema: ZodObject<T>) {
    const oldResponse = this.schemas.response!;

    const merged = oldResponse ? oldResponse.merge(schema) : schema;

    this.setResponse(merged);
    return this;
  }

  extendParamsSchema<T extends ZodRawShape>(schema: ZodObject<T>) {
    const oldParams = this.schemas.params!;

    const merged = oldParams ? oldParams.merge(schema) : schema;

    this.setParams(merged);
    return this;
  }
}
