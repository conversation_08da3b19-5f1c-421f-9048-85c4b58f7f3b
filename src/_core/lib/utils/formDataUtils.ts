/**
 * Utilidades para manejar FormData y objetos anidados
 */

/**
 * Reconstruye un objeto anidado desde FormData aplanado
 * Convierte claves como "domicilio.calle" en { domicilio: { calle: "valor" } }
 */
export function reconstructNestedObject(formData: FormData): Record<string, any> {
  const result: any = {};
  
  for (const [key, value] of formData.entries()) {
    const keys = key.split('.');
    let current = result;
    
    // Navegar/crear la estructura anidada
    for (let i = 0; i < keys.length - 1; i++) {
      const currentKey = keys[i];
      if (!(currentKey in current)) {
        current[currentKey] = {};
      }
      current = current[currentKey];
    }
    
    // Asignar el valor final
    const finalKey = keys[keys.length - 1];
    current[finalKey] = convertFormValue(value, finalKey);
  }
  
  return result;
}

/**
 * Convierte valores de FormData a sus tipos apropiados
 */
function convertFormValue(value: FormDataEntryValue, fieldName?: string): any {
  if (typeof value !== 'string') {
    return value;
  }

  // Convertir booleanos
  if (value === 'true') {
    return true;
  }
  if (value === 'false') {
    return false;
  }

  // Convertir números basado en el nombre del campo y el valor
  if (value !== '' && !isNaN(Number(value))) {
    const numValue = Number(value);

    // Campos que sabemos que deben ser números
    if (fieldName) {
      const lowerFieldName = fieldName.toLowerCase();

      // IDs siempre son números
      if (lowerFieldName.endsWith('id')) {
        return numValue;
      }

      // Campos específicos que son números
      if (lowerFieldName === 'numero' || lowerFieldName === 'cantidad' || lowerFieldName === 'precio') {
        return numValue;
      }
    }

    // Si es un número entero válido y no parece ser un string (como DNI, teléfono, etc.)
    if (Number.isInteger(numValue) && value.length <= 10) {
      // No convertir números que parecen ser identificadores como DNI, teléfonos, etc.
      if (fieldName) {
        const lowerFieldName = fieldName.toLowerCase();
        if (lowerFieldName.includes('dni') ||
            lowerFieldName.includes('telefono') ||
            lowerFieldName.includes('codigo') ||
            lowerFieldName.includes('cuit') ||
            lowerFieldName.includes('cuil')) {
          return value; // Mantener como string
        }
      }
      return numValue;
    }
  }

  // Mantener como string por defecto
  return value;
}

/**
 * Aplana un objeto anidado para FormData
 * Convierte { domicilio: { calle: "valor" } } en "domicilio.calle" = "valor"
 */
export function flattenObjectForFormData(obj: any, prefix = ''): Record<string, string> {
  const result: Record<string, string> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (value !== null && value !== undefined) {
      if (typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
        // Recursivamente aplanar objetos anidados
        Object.assign(result, flattenObjectForFormData(value, fullKey));
      } else {
        // Convertir a string
        result[fullKey] = String(value);
      }
    }
  }
  
  return result;
}
