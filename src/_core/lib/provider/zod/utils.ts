import {
  ZodType<PERSON>ny,
  ZodEnum,
  ZodNativeEnum,
  ZodObject,
} from "zod";

export type SelectOption = {
  label: string;
  value: string | number;
};

/**
 * Extrae el schema anidado de un ZodObject siguiendo la ruta (dot notation).
 * Ejemplo: getNestedSchema(schema, "direccion.provincia")
 */
function getNestedSchema(
  schema: ZodTypeAny,
  path: string
): ZodTypeAny | undefined {
  if (!(schema instanceof ZodObject)) return undefined;

  const keys = path.split(".");

  let currentSchema: ZodTypeAny | undefined = schema;

  for (const key of keys) {
    if (
      currentSchema instanceof ZodObject &&
      currentSchema.shape &&
      key in currentSchema.shape
    ) {
      currentSchema = currentSchema.shape[key];
    } else {
      return undefined;
    }
  }

  return currentSchema;
}

/**
 * Extrae opciones si el schema es un ZodEnum o ZodNativeEnum, soportando path anidados.
 *
 * @param schema schema raíz (ZodObject)
 * @param path ruta del campo (dot notation)
 */
export function extractEnumOptionsFromPath(
  schema: ZodTypeAny,
  path: string
): SelectOption[] | undefined {
  const fieldSchema = getNestedSchema(schema, path);
  if (!fieldSchema) return undefined;

  if (fieldSchema instanceof ZodEnum) {
    return fieldSchema.options.map((value: string) => ({
      value,
      label: capitalize(value),
    }));
  }

  if (fieldSchema instanceof ZodNativeEnum) {
    const values = Object.values(fieldSchema.enum).filter(
      (v) => typeof v === "string" || typeof v === "number"
    );
    const uniqueValues = Array.from(new Set(values));
    return uniqueValues.map((value) => ({
      value,
      label: capitalize(String(value)),
    }));
  }

  return undefined;
}

function capitalize(value: string): string {
  return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
}
