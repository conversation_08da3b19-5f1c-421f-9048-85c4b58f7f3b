import Link from "next/link";

export default function MultipleSessionErrorRemedy({
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div>
      <h2>No es posible tener múltiples pestañas activas</h2>
      <button onClick={() => reset()}>Utilizar esta pestaña</button>
      <div>
        <Link href="/">Volver al inicio</Link>
      </div>
    </div>
  );
}
