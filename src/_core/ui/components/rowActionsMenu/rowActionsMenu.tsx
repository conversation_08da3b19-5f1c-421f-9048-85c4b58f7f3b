"use client";

import {
  I<PERSON><PERSON><PERSON>on,
  <PERSON>u,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import { MoreVertical } from "react-feather";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { iconMap } from "@/_ui/icons/iconMap";
import { DataRow, ActionMeta } from "@/_core/utils/crud";

type Props = {
  row: DataRow;
  actions: ActionMeta[];
};

export default function RowActionsMenu({ row, actions }: Props) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const router = useRouter();

  const handleOpen = (e: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(e.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <IconButton onClick={handleOpen}>
        <MoreVertical size={20} />
      </IconButton>
      <Menu anchorEl={anchorEl} open={Bo<PERSON>an(anchorEl)} onClose={handleClose}>
        {actions.map((action) => {
          const Icon = iconMap[action.icon];
          const href = typeof action.href === "function" ? action.href(row) : action.href;

          return (
            <MenuItem
              key={action.label}
              onClick={() => {
                router.push(href);
                handleClose();
              }}
            >
              <ListItemIcon>
                <Icon size={18} />
              </ListItemIcon>
              <ListItemText>{action.label}</ListItemText>
            </MenuItem>
          );
        })}
      </Menu>
    </>
  );
}
