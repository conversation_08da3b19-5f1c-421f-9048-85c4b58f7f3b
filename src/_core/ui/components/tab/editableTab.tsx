"use client";

import { Card } from "@mui/material";
import EditButton from "@/_core/ui/components/button/editButton";
import { ReactNode } from "react";

type Props = {
  onEdit?: () => void;
  children: ReactNode;
};

export default function EditableTab({ onEdit = () => {}, children }: Props) {
  return (
    <Card className="
      relative 
      p-6 
      w-full h-full
      lg:min-h-50"
    >
      <div
        className="
        flex flex-row
        justify-between
      "
      >
        {children}
        <div className="top-4 right-4 pl-4">
          <EditButton onEdit={onEdit} />
        </div>
      </div>
    </Card>
  );
}
