"use client";

import { useState } from "react";
import { useTheme, Paper } from "@mui/material";
import clsx from "clsx";

type TabItem = {
  label: string;
  content: React.ReactNode;
};

type Props = {
  tabs: TabItem[];
};

export default function TabNavigator({ tabs }: Props) {
  const [tabIndex, setTabIndex] = useState(0);
  const theme = useTheme();

  const activeBg = theme.palette.primary.main;
  const activeText = theme.palette.primary.contrastText;

  return (
    <div className="flex flex-col gap-6">
      <Paper
        elevation={0}
        className={clsx(
          "inline-flex flex-wrap gap-2 p-2 border border-gray-300 rounded-lg",
          "w-fit max-w-full"
        )}
        sx={{ bgcolor: "background.paper", boxShadow: "0px 1px 2px rgba(0, 0, 0, 0.06)", }}
      >
        {tabs.map((tab, index) => {
          const isActive = tabIndex === index;

          return (
            <button
              key={tab.label}
              onClick={() => setTabIndex(index)}
              className={clsx(
                "px-4 py-1 rounded-full text-sm font-medium border whitespace-nowrap transition-colors",
                isActive
                  ? "shadow"
                  : "bg-transparent border-gray-300 hover:bg-gray-100"
              )}
              style={
                isActive
                  ? {
                      backgroundColor: activeBg,
                      color: activeText,
                      borderColor: activeBg,
                    }
                  : undefined
              }
            >
              {tab.label}
            </button>
          );
        })}
      </Paper>

      {/* Contenido del tab activo */}
      <div>{tabs[tabIndex]?.content}</div>
    </div>
  );
}
