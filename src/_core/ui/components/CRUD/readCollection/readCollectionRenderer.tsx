"use client";

import { ColumnRenderer, DataRow } from "@/_core/utils/crud";
import PageVariant from "./table/pageVariant";
import TabVariant from "./table/tabVariant";
import { renderers } from "./renderers";

export type ColumnConfigWithStringRenderer = {
  key: string;
  label?: string;
  renderer?: string;
  options?: Record<string, unknown>;
};

export type ColumnConfig = {
  key: string;
  label?: string;
  renderer?: ColumnRenderer;
  options?: Record<string, unknown>;
};

// Type guard to check if columnsConfig is ColumnConfigWithStringRenderer[]
function isStringRendererConfig(
  config: ColumnConfig[] | ColumnConfigWithStringRenderer[] | undefined
): config is ColumnConfigWithStringRenderer[] {
  return (
    Array.isArray(config) &&
    config.length > 0 &&
    typeof config[0].renderer === "string"
  );
}

// Convert string renderer identifiers to actual renderer functions
function convertStringRenderers(
  columnsConfig: ColumnConfigWithStringRenderer[]
): ColumnConfig[] {
  return columnsConfig.map((col) => ({
    ...col,
    renderer: col.renderer ? renderers[col.renderer] : undefined,
  }));
}

type Props = {
  data: DataRow[];
  columnsConfig?: ColumnConfig[] | ColumnConfigWithStringRenderer[];
};

type Variant = "tab" | "page";

export default function ReadCollectionRenderer({ data, columnsConfig, variant = "page" }: Props & { variant?: Variant }) {
  // Convert string renderers to actual renderer functions if needed
  const convertedColumnsConfig = isStringRendererConfig(columnsConfig)
    ? convertStringRenderers(columnsConfig)
    : columnsConfig;

  return variant === "tab" ? (
    <TabVariant data={data} columnsConfig={convertedColumnsConfig as ColumnConfig[]} />
  ) : (
    <PageVariant data={data} columnsConfig={convertedColumnsConfig as ColumnConfig[]} />
  );
}
