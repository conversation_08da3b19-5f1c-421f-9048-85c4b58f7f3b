"use client";

import HeaderTab from "@/_core/ui/components/tab/headerTab";
import TabNavigator from "@/_core/ui/components/tabNavigator/tabNavigator";

import { ReactNode } from "react";

type Badge = {
  label: string;
  bgColor: string;
  textColor: string;
};

type TabItem = {
  label: string;
  content: ReactNode;
};

type Props = {
  title: string;
  subtitle?: string;
  badges?: Badge[];
  actions?: ReactNode;
  tabs: TabItem[];
};

export default function ReadIndexRenderer({
  title,
  subtitle,
  badges = [],
  actions,
  tabs,
}: Props) {
  return (
    <div className="space-y-6">
      {/* Detalle de la entidad */}
      <HeaderTab
        title={title}
        subtitle={subtitle}
        badges={badges}
        actions={actions}
      />
      <TabNavigator
        tabs={tabs.map((tab) => ({
          label: tab.label,
          content: tab.content,
        }))}
      />
    </div>
  );
}
