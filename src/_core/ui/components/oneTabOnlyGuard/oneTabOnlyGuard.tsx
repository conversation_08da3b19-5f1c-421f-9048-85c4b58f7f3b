"use client";

import { useEffect, useState } from "react";

import { MultipleSessionError } from "@/_core/lib/context/error/badRequestError/multipleSessionError";

export default function OneTabOnlyGuard() {
    
  const [isInvalidSession, setIsInvalidSession] = useState(false);

  useEffect(() => {
    const myTabId = generarHashAleatorio();

    // Registrar esta pestaña como activa
    localStorage.setItem("active-tab-id", myTabId);

    // Verifica si otra pestaña se vuelve activa
    const handleStorage = (e: StorageEvent) => {
      if (e.key === "active-tab-id" && e.newValue !== myTabId) {
        setIsInvalidSession(true);
      }
    };

    window.addEventListener("storage", handleStorage);

    // Limpiar antes de salir
    window.addEventListener("beforeunload", () => {
      const current = localStorage.getItem("active-tab-id");
      if (current === myTabId) {
        localStorage.removeItem("active-tab-id");
      }
    });

    return () => {
      window.removeEventListener("storage", handleStorage);
    };
  }, []);

  // Lanza el error para ser capturado por el error boundary
  if (isInvalidSession) {
    throw new MultipleSessionError();
  }

  return null;
}

function generarHashAleatorio(longitud: number = 16) {
  let hash = "";
  for (let i = 0; i < longitud; i++) {
    // Genera un valor aleatorio entre 0 y 15 y lo convierte a hexadecimal
    hash += Math.floor(Math.random() * 16).toString(16);
  }
  return hash;
}
