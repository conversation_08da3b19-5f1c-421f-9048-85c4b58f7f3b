"use client";

import { Backdrop, CircularProgress } from "@mui/material";

type BackdropLoaderProps = {
  open: boolean;
};

export function BackdropLoader({ open }: BackdropLoaderProps) {
  return (
    <Backdrop
      sx={{
        bgcolor: "background.default",
        zIndex: (theme) => theme.zIndex.modal + 1000,
      }}
      open={open}
      transitionDuration={300}
    >
      <CircularProgress
        size={60}
        thickness={4.5}
        disableShrink
        color="secondary"
      />
    </Backdrop>
  );
}
