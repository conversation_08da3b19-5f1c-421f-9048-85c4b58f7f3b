"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>on, Stack, Typography } from "@mui/material";
import { Filter } from "react-feather";

export default function FiltersButton({ onEdit }: { onEdit: () => void }) {
  return (
    <Stack
      direction="row"
      justifyContent={"flex-end"}
      alignItems="center"
      spacing={2}
    >
      {/* TODO: Agregar filtros aplicados */}
      <IconButton onClick={onEdit}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Filter />
          <Typography
            sx={{ typography: { xs: "body2", md: "body1xl", lg: "body1xl" } }}
          >
            Filtros
          </Typography>
          <Badge color="primary" badgeContent={0} />
        </Stack>
      </IconButton>
    </Stack>
  );
}
