"use client";

import { IconButton, Typography } from "@mui/material";
import { RefreshCw } from "react-feather";

export default function EstadoUpdaterButton({
  onEstadoUpdate,
}: {
  onEstadoUpdate: () => void;
}) {
  return (
    <IconButton onClick={onEstadoUpdate}>
      <RefreshCw />
      <Typography
        className="ml-2"
        sx={{ typography: { xs: "body2", md: "body1xl", lg: "body1xl" } }}
      >
        Actualizar estado
      </Typography>
    </IconButton>
  );
}
