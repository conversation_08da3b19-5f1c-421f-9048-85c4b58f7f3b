"use client";

import { useRouter } from "next/navigation";
import { Button, Stack, Typography } from "@mui/material";
import { PlusCircle } from "react-feather";

export default function CreateButton({
  entityName,
}: {
  entityName: string;
}) {
  const router = useRouter();

  return (
    <Button
      onClick={() => router.push(`/${entityName}/create`)}
      sx={{ borderWidth: 3, borderRadius: "12px", borderStyle: "solid" }}
      variant="outlined"
      color="primary"
    >
      <Stack direction="row" alignItems="center" className="gap-2 py-3 px-6">
        <PlusCircle />
        <Typography
          className="ml-2"
          sx={{ typography: { xs: "body2", md: "body1xl", lg: "body1xl" } }}
        >
          Nuevo
        </Typography>
      </Stack>
    </Button>
  );
}
