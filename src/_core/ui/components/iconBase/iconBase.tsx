import * as React from "react";
import type { SVGProps } from "react";

type IconBaseProps = SVGProps<SVGSVGElement> & {
  size?: number;
  color?: string;
  children?: React.ReactNode;
};

const IconBase = React.forwardRef<SVGSVGElement, IconBaseProps>(
  (
    { size = 24, color = "currentColor", strokeWidth = 2, children, ...props },
    ref
  ) => (
    <svg
      ref={ref}
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      {children}
    </svg>
  )
);

IconBase.displayName = "IconBase";

export default IconBase;
