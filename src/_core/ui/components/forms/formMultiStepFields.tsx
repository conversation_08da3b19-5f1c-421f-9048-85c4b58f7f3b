"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Paper,
  Typography,
} from "@mui/material";
import { FieldValues, useFormContext, useFormState } from "react-hook-form";
import { useEffect, useState } from "react";
import { FormFields } from "./formFields";
import { FieldMeta } from "./types";

type StepConfig<T> = {
  label: string;
  fields: (keyof T & string)[];
};

type MultiStepFormProps<T extends FieldValues> = {
  steps: StepConfig<T>[];
  meta: Record<keyof T & string, FieldMeta>;
};

export function FormMultiStepFields<T extends FieldValues>({
  steps,
  meta,
}: MultiStepFormProps<T>) {
  const [activeStep, setActiveStep] = useState(0);
  const { control } = useFormContext<T>();
  const { errors, isSubmitted } = useFormState({ control });

  const isFirstStep = activeStep === 0;
  const isLastStep = activeStep === steps.length - 1;

  const handleNext = () => {
    if (!isLastStep) {
      setActiveStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    if (!isFirstStep) {
      setActiveStep((prev) => prev - 1);
    }
  };

  // Función segura para saber si un campo tiene error usando dot notation
  const hasError = (errors: FieldValues, path: string): boolean => {
    const parts = path.split(/[\.\[\]]+/).filter(Boolean);
    return parts.reduce((acc, key) => acc?.[key], errors) !== undefined;
  };

  // Detecta el primer paso con error tras un submit
  useEffect(() => {
    if (!isSubmitted || Object.keys(errors).length === 0) return;

    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      const hasErrorInStep = step.fields.some((fieldName) =>
        hasError(errors, fieldName)
      );
      if (hasErrorInStep) {
        setActiveStep(i);
        break;
      }
    }
  }, [isSubmitted, errors, steps]);

  return (
    <Stack direction="column" spacing={2}>
      <Paper elevation={3} sx={{ bgcolor: "third.dark" }} className="py-4">
        <Stepper activeStep={activeStep} connector={null} className="px-4 lg:px-20">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel
                slotProps={{
                  stepIcon: {
                    icon: (
                      <Typography
                        sx={{
                          typography: {
                            xs: "body2",
                            md: "body6xl",
                            lg: "body6xl",
                          },
                        }}
                        color={
                          activeStep === index
                            ? "primary.main"
                            : "text.secondary"
                        }
                      >
                        {`${index + 1}.`}
                      </Typography>
                    ),
                  },
                }}
              >
                <Typography
                  sx={{
                    typography: { xs: "body2", md: "body6xl", lg: "body6xl" },
                  }}
                  color={
                    activeStep === index ? "primary.main" : "text.secondary"
                  }
                >
                  {step.label}
                </Typography>
              </StepLabel>
            </Step>
          ))}
        </Stepper>
      </Paper>

      <Paper elevation={3} className="py-8">
        <Stack direction="column" spacing={2} className="px-4 xl:px-23 2xl:px-47">
          {steps.map((step, index) => {
            const stepMeta = Object.fromEntries(
              step.fields.map((key) => [key, meta[key]])
            ) as Record<string, FieldMeta>;

            return (
              <Box key={step.label} hidden={index !== activeStep}>
                <FormFields<T> meta={stepMeta} />
              </Box>
            );
          })}

          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
            {isFirstStep ? (
              <div />
            ) : (
              <Button
                variant="outlined"
                onClick={handleBack}
                disabled={isFirstStep}
              >
                Atrás
              </Button>
            )}

            {isLastStep ? (
              <div />
            ) : (
              <Button
                variant="contained"
                onClick={handleNext}
                disabled={isLastStep}
              >
                Siguiente
              </Button>
            )}
          </Box>
        </Stack>
      </Paper>
    </Stack>
  );
}
