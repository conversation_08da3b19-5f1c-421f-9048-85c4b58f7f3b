"use client";

import { Button } from "@mui/material";
import { useFormContext } from "react-hook-form";

export function FormSubmitButton({ label = "Enviar" }: { label?: string }) {
  const { formState } = useFormContext();
  const isValid = formState.isValid;

  return (
    <Button
      fullWidth
      type="submit"
      disabled={!isValid}
      variant="contained"
      color="primary"
      className="rounded-xl"
      sx={{ typography: { lg: "body1xl", md: "body3xl", xs: "body3" } }}
    >
      {label}
    </Button>
  );
}
