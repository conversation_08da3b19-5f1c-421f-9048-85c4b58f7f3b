"use client";

import { DefaultValues } from "react-hook-form";
import { ReactNode } from "react";
import {
  schemas,
  InferSchema,
  ActionKeys,
  EntityKeys,
} from "@/_lib/data/model/schema";
import { FormContextProvider } from "./formContextProvider";
import { FormFields } from "./formFields";
import { FormMultiStepFields } from "./formMultiStepFields";
import { FieldMeta, Step } from "./types";
import { LoadStaticDataError } from "@/_core/lib/context/error";
import { ServerActionParams } from "@/_lib/data/model/action/actionFactory";

type ClientFormWrapperProps<
  E extends EntityKeys,
  A extends ActionKeys<E>
> = {
  entityName: E;
  actionName: A;
  meta: Record<keyof InferSchema<E, A>, FieldMeta>;
  defaultValues?: DefaultValues<InferSchema<E, A>>;
  action?: (formData: ServerActionParams) => Promise<unknown>;
  steps?: Step<E, A>[];
  header?: ReactNode;
  children?: ReactNode;
};

export function ClientFormWrapper<
  E extends EntityKeys,
  A extends ActionKeys<E>
>({
  entityName,
  actionName,
  meta,
  defaultValues,
  action,
  steps,
  header,
  children,
}: ClientFormWrapperProps<E, A>) {
  const schema = schemas?.[entityName]?.[actionName]?.body;

  if (!schema) {
    throw new LoadStaticDataError(`${entityName}.${actionName}`);
  }

  return (
    <FormContextProvider<InferSchema<E, A>>
      schema={schema}
      defaultValues={defaultValues}
      action={action}
      entityName={entityName}
      actionName={actionName}
    >
      {header}
      {steps ? (
        <FormMultiStepFields<InferSchema<E, A>> steps={steps} meta={meta} />
      ) : (
        <FormFields<InferSchema<E, A>> meta={meta} />
      )}
      {children}
    </FormContextProvider>
  );
}
