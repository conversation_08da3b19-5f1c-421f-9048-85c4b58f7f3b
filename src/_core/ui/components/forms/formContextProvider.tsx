"use client";

import { ReactNode } from "react";
import {
  useForm,
  FormProvider,
  FieldValues,
  DefaultValues,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ZodTypeAny } from "zod";
import { useMutation } from "@tanstack/react-query";

import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs';
import 'dayjs/locale/es';

import { FormSchemaContext } from "./hooks/useFormSchema";

import { runFormAction } from "@/_lib/data/model/action/actionFactory";
import { EntityKeys } from "@/_lib/utils/entities";
import { ServerActionParams } from "@/_lib/data/model/action/actionFactory";
import { flattenObjectForFormData } from "@/_core/lib/utils/formDataUtils";

export type MutationVariables = {
  formData: FormData;
  entityName: EntityKeys;
  actionName: string;
};

type FormContextProviderProps<T extends FieldValues> = {
  schema: ZodTypeAny;
  children: ReactNode;
  defaultValues?: DefaultValues<T>;
  action?: (formData: ServerActionParams) => Promise<unknown>;
  entityName: EntityKeys;
  actionName: string;
};

export function FormContextProvider<T extends FieldValues>({
  schema,
  children,
  defaultValues,
  action,
  entityName,
  actionName,
}: FormContextProviderProps<T>) {
  const methods = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: "onChange",
  });

  const mutation = useMutation<void, Error, MutationVariables>({
    mutationKey: [entityName, actionName],
    mutationFn: async ({ formData }) => {
      if (action) {
        await action({ body: formData } as ServerActionParams);
      }
      else {
        await runFormAction(entityName, actionName, { body: formData } as ServerActionParams);
      }
    },
  });

  const onSubmit = async (values: T) => {
    const formData = new FormData();

    // Usar la función utilitaria para aplanar el objeto
    const flattenedData = flattenObjectForFormData(values);

    // Agregar todos los campos aplanados al FormData
    for (const [key, value] of Object.entries(flattenedData)) {
      formData.append(key, value);
    }

    await mutation.mutateAsync({ formData, entityName, actionName });
  };

  return (
    <FormSchemaContext.Provider value={schema}>
      <FormProvider {...methods}>
        <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="es">
          <form onSubmit={methods.handleSubmit(onSubmit)}>{children}</form>
        </LocalizationProvider>
      </FormProvider>
    </FormSchemaContext.Provider>
  );
}
