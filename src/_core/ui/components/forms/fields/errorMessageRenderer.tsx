import { Typography, Box } from "@mui/material";
import { FieldError } from "react-hook-form";

export function ErrorMessageRenderer({ error }: { error?: FieldError }) {
  if (!error?.message) return null;

  return (
    <Box component="span" sx={{ color: "error.main", mt: 0.5, ml: 1.5 }}>
      <Typography
        className="align-top"
        sx={{ typography: { xs: "body3", md: "body3xl", lg: "body2xl" } }}
      >
        {error.message.toString()}
      </Typography>
    </Box>
  );
}
