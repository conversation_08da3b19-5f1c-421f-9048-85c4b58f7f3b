"use client";

import { useForm<PERSON>ontext, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Controller } from "react-hook-form";
import { FieldMeta } from "../types";
import { ErrorMessageRenderer } from "./errorMessageRenderer";
import { FormControl, Typography } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { useFieldError } from "../hooks/useFieldError";
import dayjs from "dayjs";

export default function DateFieldRenderer<T extends FieldValues>({
  name,
  config,
}: {
  name: Path<T>;
  config: FieldMeta;
}) {
  const { control } = useFormContext<T>();
  const error = useFieldError<T>(name);

  return (
    <FormControl fullWidth>
      <Controller
        control={control}
        name={name}
        render={({ field }) => {
          const { value, onChange, ...rest } = field;

          return (
            <DatePicker
              {...rest}
              value={value ? dayjs(value) : null}
              onChange={(date) => {
                onChange(date ? date.format("YYYY-MM-DD") : "");
              }}
              label={
                <Typography
                  className="align-top"
                  sx={{ typography: { xs: "body3", md: "body3xl", lg: "body2xl" } }}
                  color="text.secondary"
                >
                  {config.label}
                </Typography>
              }
              slotProps={{
                textField: {
                  fullWidth: true,
                  error: !!error,
                  variant: config.variant ?? "outlined",
                  size: config.size ?? "medium",
                },
              }}
            />
          );
        }}
      />
      <ErrorMessageRenderer error={error} />
    </FormControl>
  );
}
