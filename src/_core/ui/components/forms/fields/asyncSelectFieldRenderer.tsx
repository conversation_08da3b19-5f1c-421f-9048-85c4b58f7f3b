"use client";

import React, { useState, useMemo } from "react";
import { useFormContext, FieldValues, Path } from "react-hook-form";
import {
  FormControl,
  TextField,
  Autocomplete,
  CircularProgress,
  FormHelperText,
} from "@mui/material";
import { useInfiniteQuery } from "@tanstack/react-query";

import { useFieldError } from "../hooks/useFieldError";
import { FieldMeta, SelectOption } from "../types";
import { ErrorMessageRenderer } from "./errorMessageRenderer";

import { runFormAction } from "@/_lib/data/model/action/actionFactory";
import { EntityKeys } from "@/_lib/utils/entities";

type AsyncSelectFieldRendererProps<T extends FieldValues> = {
  name: Path<T>;
  config: FieldMeta;
};

// Type for the API response page
type ApiResponsePage = {
  data: SelectOption[];
  hasNextPage: boolean;
};

// Type guard for supported entities
const isSupportedEntity = (entity: string): entity is EntityKeys => {
  return ["token", "asociados", "localidades"].includes(entity);
};

// Function to map API response to SelectOption
const mapToSelectOptions = (data: any[], labelKey: string, valueKey: string): SelectOption[] => {
  return data.map((item: any) => {
    // Handle different possible field names for label
    let label = "";
    if (item[labelKey]) {
      label = String(item[labelKey]);
    } else if (item.nombre) {
      label = String(item.nombre);
    } else if (item.descripcion) {
      label = String(item.descripcion);
    } else if (item.name) {
      label = String(item.name);
    } else {
      label = String(item[valueKey] ?? item.id ?? "Sin nombre");
    }
    
    // Handle value
    const value = item[valueKey] ?? item.id ?? "";
    
    return { label, value };
  });
};

export default function AsyncSelectFieldRenderer<T extends FieldValues>({
  name,
  config,
}: AsyncSelectFieldRendererProps<T>) {
  const { watch, setValue } = useFormContext<T>();
  const fieldValue = watch(name) ?? "";
  const fieldError = useFieldError<T>(name);
  
  const [inputValue, setInputValue] = useState("");
  
  // Extract configuration with defaults
  const labelKey = config.labelKey ?? "nombre"; // Default to "nombre" for localidades
  const valueKey = config.valueKey ?? "id"; // Default to "id" for localidades
  const debounceDelay = config.debounceDelay ?? 300;
  const pageSize = config.pageSize ?? 20;
  const searchField = config.searchField ?? "search";
  
  // Validate entity
  const entity = config.entity;
  if (entity && !isSupportedEntity(entity)) {
    console.error(`Unsupported entity: ${entity}`);
  }
  
  // Use infinite query for pagination
  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
  } = useInfiniteQuery<ApiResponsePage, Error>({
    queryKey: ["asyncSelect", entity, inputValue],
    queryFn: async ({ pageParam = 1 }) => {
      if (!entity || !isSupportedEntity(entity)) {
        return { data: [], hasNextPage: false };
      }
      
      try {
        // Prepare params for the API call
        const params: Record<string, string | number> = {
          page: pageParam as number,
          limit: pageSize,
        };
        
        // Add search parameter if provided
        if (inputValue) {
          params[searchField] = inputValue;
        }
        
        // Call the server action
        const result = await runFormAction(entity, "collection", {
          params,
        });
        
        // Process the response
        if (result && typeof result === "object" && "data" in result) {
          const apiData = result.data;
          if (Array.isArray(apiData)) {
            const options = mapToSelectOptions(apiData, labelKey, valueKey);
            return {
              data: options,
              hasNextPage: apiData.length === pageSize,
            };
          }
        }
        
        return { data: [], hasNextPage: false };
      } catch (err) {
        console.error("Error fetching options:", err);
        throw new Error("Error al cargar las opciones");
      }
    },
    getNextPageParam: (lastPage, pages) => {
      return lastPage.hasNextPage ? pages.length + 1 : undefined;
    },
    initialPageParam: 1,
    enabled: !!entity && isSupportedEntity(entity),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes (cacheTime was renamed to gcTime in newer versions)
  });
  
  // Flatten the paginated data
  const allOptions = useMemo(() => {
    if (!data) return [];
    return data.pages.flatMap(page => page.data);
  }, [data]);
  
  // Find the selected option
  const selectedOption = useMemo(() => {
    if (!fieldValue) return null;
    return allOptions.find(option => option.value === fieldValue) || null;
  }, [allOptions, fieldValue]);
  
  // Handle input change with debounce
  const handleInputChange = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    
    return (value: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setInputValue(value);
      }, debounceDelay);
    };
  }, [debounceDelay]);
  
  return (
    <FormControl fullWidth error={!!fieldError || !!error}>
      <Autocomplete
        value={selectedOption}
        onChange={(_, newValue) => {
          // Convert to number if the field name suggests it's an ID field
          const value = newValue ? newValue.value : "";
          const finalValue = name.toLowerCase().includes('id') && value ? Number(value) : value;
          setValue(name, finalValue as any, { shouldValidate: true });
        }}
        inputValue={inputValue}
        onInputChange={(_, newInputValue) => {
          setInputValue(newInputValue);
          handleInputChange(newInputValue);
        }}
        options={allOptions}
        getOptionLabel={(option) => (typeof option === 'string' ? option : option.label)}
        isOptionEqualToValue={(option, value) => option.value === value.value}
        loading={isFetching && !isFetchingNextPage}
        loadingText="Cargando..."
        noOptionsText="No se encontraron opciones"
        openText="Abrir"
        closeText="Cerrar"
        clearText="Limpiar"
        size={config.size ?? "small"}
        renderInput={(params) => (
          <TextField
            {...params}
            label={config.label}
            placeholder={`Buscar ${config.label ?? ""}`}
            variant={config.variant ?? "outlined"}
            error={!!fieldError || !!error}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <>
                  {isFetching && !isFetchingNextPage ? <CircularProgress color="inherit" size={20} /> : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            }}
          />
        )}
        ListboxProps={{
          onScroll: (event) => {
            const listboxNode = event.currentTarget;
            if (
              listboxNode.scrollTop + listboxNode.clientHeight >=
              listboxNode.scrollHeight - 5 &&
              hasNextPage &&
              !isFetchingNextPage
            ) {
              fetchNextPage();
            }
          },
        }}
      />
      {error && (
        <FormHelperText error>{error instanceof Error ? error.message : "Error desconocido"}</FormHelperText>
      )}
      <ErrorMessageRenderer error={fieldError} />
    </FormControl>
  );
}
