"use client";

import { useForm<PERSON>ontext, <PERSON>V<PERSON><PERSON>, Path } from "react-hook-form";
import { FieldMeta } from "../types";

import { Checkbox, FormControlLabel, Typography } from "@mui/material";

export default function CheckboxFieldRenderer<T extends FieldValues>({
  name,
  config,
}: {
  name: string;
  config: FieldMeta;
}) {
  const { register } = useFormContext<T>();

  return (
    <FormControlLabel
      color={config.color ?? "secondary"}
      control={<Checkbox {...register(name as Path<T>)} />}
      label={<Typography sx={{ typography: { xs: "body2", md: "body2xl" } }}>{config.label} </Typography>}
    />
  );
}
