"use client";

import React from "react";
import { useFormContext, FieldValues, Path } from "react-hook-form";
import {
  FormControl,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";

import { useFieldError } from "../hooks/useFieldError";
import { FieldMeta } from "../types";
import { useFormSchema } from "../hooks/useFormSchema";
import {
  extractEnumOptionsFromPath,
  SelectOption,
} from "@/_core/lib/provider/zod/utils";

import { ErrorMessageRenderer } from "./errorMessageRenderer";

type SelectFieldRendererProps<T extends FieldValues> = {
  name: Path<T>;
  config: FieldMeta;
};

export default function SelectFieldRenderer<T extends FieldValues>({
  name,
  config,
}: SelectFieldRendererProps<T>) {
  const { register, watch } = useFormContext<T>();
  const schema = useFormSchema();
  const error = useFieldError<T>(name);
  const value = watch(name) ?? "";

  // Extraer opciones: usa options del config o enum extraído del schema
  const options: SelectOption[] =
    config.options ?? extractEnumOptionsFromPath(schema, name as string) ?? [];

  return (
    <FormControl fullWidth error={!!error}>
      <Select
        displayEmpty
        value={value}
        variant={config.variant ?? "outlined"}
        size={config.size ?? "medium"}
        {...register(name)}
      >
        <MenuItem disabled value="">
          <Typography
            sx={{ typography: { xs: "body3", md: "body3xl", lg: "body2xl" } }}
            color="text.secondary"
          >
            {config.label}
          </Typography>
        </MenuItem>
        {options.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
      <ErrorMessageRenderer error={error} />
    </FormControl>
  );
}
