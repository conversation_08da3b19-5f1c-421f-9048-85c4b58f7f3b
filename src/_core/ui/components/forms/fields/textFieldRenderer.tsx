"use client";

import { useFormContext, FieldVal<PERSON>, Path } from "react-hook-form";
import { useFieldError } from "../hooks/useFieldError";
import { FieldMeta } from "../types";

import { ErrorMessageRenderer } from "./errorMessageRenderer";

import { FormControl, TextField, Typography } from "@mui/material";

export default function TextFieldRenderer<T extends FieldValues>({
  name,
  config,
}: {
  name: Path<T>;
  config: FieldMeta;
}) {
  const { register } = useFormContext<T>();
  const error = useFieldError<T>(name);

  return (
    <FormControl fullWidth>
      <TextField
        fullWidth
        type={config.type}
        label={
          <Typography
            className="align-top"
            sx={{ typography: { xs: "body3", md: "body3xl", lg: "body2xl" } }}
          >
            {config.label}
          </Typography>
        }
        variant={config.variant ?? "outlined"}
        size={config.size ?? "medium"}
        {...register(name)}
        error={!!error}
      />
      <ErrorMessageRenderer error={error} />
    </FormControl>
  );
}
