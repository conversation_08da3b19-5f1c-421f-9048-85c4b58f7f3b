// fields/groupFieldRenderer.tsx

"use client";

import React from "react";
import { Box, Typography } from "@mui/material";
import { FieldValues } from "react-hook-form";
import { FieldMeta } from "../types";
import { FormFields } from "../formFields";

export default function GroupFieldRenderer({
  config,
}: {
  name: string;
  config: FieldMeta;
}) {
  if (!config.fields) return null;

  return (
    <Box>
      {config.label && (
        <Typography  sx={{ mb: 2, mt: 4, typography: { xs: "body2", md: "body2xl" } }}>
          {config.label}
        </Typography>
      )}
      <FormFields<FieldValues> meta={config.fields} />
    </Box>
  );
}
