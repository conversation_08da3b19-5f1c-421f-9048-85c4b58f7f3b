import { useFormContext, FieldValues, FieldError } from "react-hook-form";

export function useFieldError<T extends FieldValues>(name: string): FieldError | undefined {
  const { formState } = useFormContext<T>();

  return name
    .split(/[\.\[\]]+/)
    .filter(Boolean)
    .reduce((acc: unknown, key) => {
      if (acc && typeof acc === "object" && key in acc) {
        return (acc as Record<string, unknown>)[key];
      }
      return undefined;
    }, formState.errors) as FieldError | undefined;
}
