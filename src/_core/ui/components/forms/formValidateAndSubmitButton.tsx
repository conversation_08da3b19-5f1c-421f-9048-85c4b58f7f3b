"use client";

import { Button } from "@mui/material";
import { useFormContext } from "react-hook-form";

export function FormValidateAndSubmitButton({ label = "Enviar" }: { label?: string }) {
  const { formState } = useFormContext();
  const isValid = formState.isValid;

  return (
    <Button
      disabled={!isValid}
      type="submit"
      variant="contained"
      color="primary"
    >
      {label}
    </Button>
  );
}
