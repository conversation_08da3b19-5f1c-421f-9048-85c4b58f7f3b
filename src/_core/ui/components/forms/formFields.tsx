"use client";

import React from "react";
import dynamic from "next/dynamic";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import { FieldValues } from "react-hook-form";

import { FieldMeta, FieldType, FieldGrid } from "./types";

// Import dinámico de los renderers
const PasswordFieldRenderer = dynamic(
  () => import("./fields/passwordFieldRenderer")
);
const TextFieldRenderer = dynamic(() => import("./fields/textFieldRenderer"));
const CheckboxFieldRenderer = dynamic(
  () => import("./fields/checkboxFieldRenderer")
);
const UsernameFieldRenderer = dynamic(
  () => import("./fields/usernameFieldRenderer")
);
const IntegerFieldRenderer = dynamic(
  () => import("./fields/integerFieldRenderer")
);
const NativeDateFieldRenderer = dynamic(
  () => import("./fields/dateFieldRenderer")
);
const SelectFieldRenderer = dynamic(
  () => import("./fields/selectFieldRenderer")
);
const GroupFieldRenderer = dynamic(
  () => import("./fields/groupFieldRenderer")
);

const AsyncSelectFieldRenderer = dynamic(
  () => import("./fields/asyncSelectFieldRenderer")
);

const fieldRendererMap: Record<
  FieldType,
  (props: { name: string; config: FieldMeta }) => React.ReactElement
> = {
  text: (props) => <TextFieldRenderer {...props} />,
  email: (props) => <TextFieldRenderer {...props} />,
  number: (props) => <TextFieldRenderer {...props} />,
  checkbox: (props) => <CheckboxFieldRenderer {...props} />,
  password: (props) => <PasswordFieldRenderer {...props} />,
  username: (props) => <UsernameFieldRenderer {...props} />,
  integer: (props) => <IntegerFieldRenderer {...props} />,
  date: (props) => <NativeDateFieldRenderer {...props} />,
  select: (props) => <SelectFieldRenderer {...props} />,
  asyncSelect: (props) => <AsyncSelectFieldRenderer {...props} />,
  group: (props) => <GroupFieldRenderer {...props} />,
};

type FormFieldsProps<T extends FieldValues> = {
  meta: Record<keyof T & string, FieldMeta>;
};

export function FormFields<T extends FieldValues>({
  meta,
}: FormFieldsProps<T>) {
  const theme = useTheme();
  const isLgUp = useMediaQuery(theme.breakpoints.up("lg"));
  const defaultSize = isLgUp ? "medium" : "small";

  // Fallback completo para tamaños de columna
  const defaultGrid: Required<FieldGrid> = {
    xs: 12,
    sm: 12,
    md: 12,
    lg: 12,
    xl: 12,
  };

  return (
    <Box
      sx={{
        display: "grid",
        gridTemplateColumns: "repeat(12, 1fr)",
        gap: { xs: 1, md: 2 },
      }}
    >
      {Object.entries(meta).map(([name, config]) => {
        const Renderer = fieldRendererMap[config.type];
        const grid = { ...defaultGrid, ...config.grid };

        return (
          <Box
            key={name}
            sx={{
              gridColumn: {
                xs: `span ${grid.xs}`,
                sm: `span ${grid.sm}`,
                md: `span ${grid.md}`,
                lg: `span ${grid.lg}`,
                xl: `span ${grid.xl}`,
              },
            }}
          >
            <Renderer
              name={name}
              config={{ ...config, size: config.size ?? defaultSize }}
            />
          </Box>
        );
      })}
    </Box>
  );
}
