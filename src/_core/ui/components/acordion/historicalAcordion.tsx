"use client";

import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
} from "@mui/material";
import { ChevronDown } from "react-feather";
import { ReactNode, useState } from "react";

type Item = {
  label: string;
  value?: string | ReactNode;
};

type Props = {
  title: string;
  icon?: ReactNode;
  items: Item[];
};

export default function HistoricalAccordion({ title = "Historial", icon, items }: Props) {
  const [expanded, setExpanded] = useState(false);

  return (
    <Accordion
      expanded={expanded}
      onChange={(_, isExpanded) => setExpanded(isExpanded)}
      className="shadow-none border-0 rounded-none"
      disableGutters
    >
      <AccordionSummary
        expandIcon={null}
        className="font-semibold text-sm px-0"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="flex items-center gap-2">
          <ChevronDown
            className={`text-gray-500 transition-transform duration-200 ${
              expanded ? "rotate-180" : ""
            }`}
            size={20}
          />
          {icon}
          <Typography variant="body1" color="text.secondary">
            {title}
          </Typography>
        </div>
      </AccordionSummary>

      <AccordionDetails className="pl-0 space-y-2">
        {items.map((item, i) => (
          <Typography key={i} variant="body1" color="text.secondary">
            <strong>{item.label}</strong>{" "}
            {item.value !== undefined ? item.value : null}
          </Typography>
        ))}
      </AccordionDetails>
    </Accordion>
  );
}
