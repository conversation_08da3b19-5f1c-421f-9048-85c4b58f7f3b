"use client";

import { toast } from "sonner";
import { Alert, AlertTitle } from "@mui/material";

export function showErrorToast(message: string) {
  toast.custom((t) => (
    <Alert 
      severity="error"
      onClose={() => toast.dismiss(t)}
      sx={{
        borderLeft: "5px solid",
        borderColor: "error.main",
        bgcolor: "error.light",
        maxWidth: "450px",
        minWidth: "300px",
      }}
    >
      <AlertTitle>Error</AlertTitle>
      {message}
    </Alert>
  ));
}
