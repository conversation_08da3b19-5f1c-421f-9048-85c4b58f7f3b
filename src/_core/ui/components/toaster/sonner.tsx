"use client";

import { useTheme as useMuiTheme } from "@mui/material/styles";
import { useMediaQuery } from "@mui/material";
import { Toaster as Sonner, ToasterProps } from "sonner";

const Toaster = ({ ...props }: ToasterProps) => {
  const muiTheme = useMuiTheme();

  // Detecta si el viewport es "md" o mayor
  const isMdUp = useMediaQuery(muiTheme.breakpoints.up("md"));

  const sonnerTheme: ToasterProps["theme"] =
    muiTheme.palette.mode === "dark" ? "dark" : "light";

  return (
    <Sonner
      theme={sonnerTheme}
      position={isMdUp ? "top-right" : "bottom-center"}
      duration={10000}
      closeButton
      expand
      className="toaster group"
      style={
        {
          "--normal-bg": muiTheme.palette.background.paper,
          "--normal-text": muiTheme.palette.text.primary,
          "--normal-border": muiTheme.palette.divider,
        } as React.CSSProperties
      }
      {...props}
    />
  );
};

export { Toaster };
