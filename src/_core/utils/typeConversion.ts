/**
 * Type conversion utilities for safely converting unknown values to specific types
 * These functions provide safe type conversion with fallback values
 */

/**
 * Safely converts unknown value to string
 * @param value - The value to convert
 * @param fallback - Fallback value if conversion fails (default: empty string)
 * @returns String representation of the value
 */
export function toString(value: unknown, fallback: string = ''): string {
  if (value === null || value === undefined) return fallback;
  return String(value);
}

/**
 * Safely converts unknown value to number
 * @param value - The value to convert
 * @param fallback - Fallback value if conversion fails (default: 0)
 * @returns Numeric representation of the value
 */
export function toNumber(value: unknown, fallback: number = 0): number {
  const num = Number(value);
  return isNaN(num) ? fallback : num;
}

/**
 * Converts query parameters from Record<string, unknown> to Record<string, string | number | boolean>
 * @param data - The data to convert
 * @returns Converted query parameters
 */
export function toQueryParams(data: Record<string, unknown>): Record<string, string | number | boolean> {
  const result: Record<string, string | number | boolean> = {};
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      result[key] = value;
    } else if (value !== null && value !== undefined) {
      result[key] = toString(value);
    }
  }
  return result;
}
