/**
 * Common data types for CRUD operations
 * Centralized type definitions to ensure consistency across CRUD components
 */

import React from "react";
import { IconKey } from "@/_ui/icons/iconMap";

/**
 * Represents a generic data row in CRUD operations
 */
export type DataRow = Record<string, unknown>;

/**
 * Options for column rendering
 */
export type RendererOptions = Record<string, unknown>;

/**
 * Action metadata for row actions
 */
export type ActionMeta = {
  label: string;
  icon: IconKey;
  href: string | ((row: DataRow) => string);
};

/**
 * Column renderer function type
 */
export type ColumnRenderer = (
  value: unknown,
  row: DataRow,
  columnName: string,
  options?: RendererOptions
) => React.ReactNode;

/**
 * Configuration for table columns
 */
export type ColumnConfig = {
  key: string;
  label?: string;
  renderer?: ColumnRenderer;
  options?: RendererOptions;
};
