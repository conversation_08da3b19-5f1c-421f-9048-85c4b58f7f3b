# Docker Configuration for fl-nextjs-core

Este proyecto incluye configuración Docker completa para desarrollo y producción, manteniendo todas las funcionalidades de SSR, middleware y compatibilidad con Traefik.

## Archivos Docker

- `Dockerfile`: Configuración multi-stage para desarrollo y producción
- `docker-compose.yml`: Configuración base para desarrollo
- `docker-compose.prod.yml`: Configuración específica para producción
- `.env.development`: Variables de entorno para desarrollo
- `.env.production`: Variables de entorno para producción

## Modo Desarrollo

### Opción 1: Usando archivo .env.development
```bash
# Copiar variables de desarrollo
cp .env.development .env

# Levantar el servicio
docker-compose up --build
```

### Opción 2: Variables inline
```bash
# Levantar directamente con variables de desarrollo
DOCKER_TARGET=dev SERVICE_NAME=fl-nextjs-core-dev docker-compose up --build
```

**Características del modo desarrollo:**
- ✅ Hot reload habilitado y funcionando
- ✅ WebSocket support automático para live reload
- ✅ Volumen montado para cambios en tiempo real
- ✅ Puerto 3000 expuesto
- ✅ Accesible en: `http://fl-nextjs-core.local` (via Traefik)
- ✅ Detección automática de cambios en archivos

## Modo Producción

### Opción 1: Usando archivo .env.production
```bash
# Copiar variables de producción
cp .env.production .env

# Levantar con configuración de producción
docker-compose -f docker-compose.prod.yml up --build
```

### Opción 2: Variables inline
```bash
# Levantar directamente con variables de producción
DOCKER_TARGET=production SERVICE_NAME=fl-nextjs-core-prod docker-compose -f docker-compose.prod.yml up --build
```

**Características del modo producción:**
- Aplicación optimizada y compilada
- Sin volúmenes montados
- Health check habilitado
- Headers de seguridad configurados
- SSL/TLS support via Traefik
- Accesible en: `https://fl-nextjs-core.prod` (via Traefik)

## Variables de Entorno Configurables

### Desarrollo (.env.development)
- `SERVICE_NAME`: Nombre del contenedor (default: fl-nextjs-core-dev)
- `TRAEFIK_HOST`: Host para Traefik (default: fl-nextjs-core.local)
- `TRAEFIK_ENTRYPOINT`: Entrypoint de Traefik (default: web)
- `DOCKER_NETWORK`: Red Docker (default: devel)

### Producción (.env.production)
- `SERVICE_NAME`: Nombre del contenedor (default: fl-nextjs-core-prod)
- `TRAEFIK_HOST`: Host para Traefik (default: fl-nextjs-core.prod)
- `TRAEFIK_ENTRYPOINT`: Entrypoint de Traefik (default: websecure)
- `DOCKER_NETWORK`: Red Docker (default: production)

## Requisitos Previos

1. **Red Docker**: Asegúrate de que las redes Docker existan:
```bash
# Para desarrollo
docker network create devel

# Para producción
docker network create production
```

2. **Traefik**: Debe estar corriendo y configurado con los entrypoints correspondientes.

## Funcionalidades Mantenidas

✅ **Server-Side Rendering (SSR)**: Completamente funcional
✅ **Middleware**: Funciona correctamente
✅ **API Routes**: Todas las rutas API funcionan
✅ **Static Generation**: Optimización de archivos estáticos
✅ **Hot Reload**: Solo en desarrollo
✅ **TypeScript**: Soporte completo
✅ **Tailwind CSS**: Configuración preservada

## Health Check

El endpoint `/api/health` está disponible para verificar el estado del servicio:
```bash
curl http://localhost:3000/api/health
```

## Solución de Problemas

### Error 502 Bad Gateway
Si experimentas un error 502 Bad Gateway:

1. **Verifica que Next.js esté escuchando en 0.0.0.0**: El Dockerfile ya está configurado para esto.
2. **Verifica el archivo /etc/hosts**: Asegúrate de que `fl-nextjs-core.local` apunte a `127.0.0.1`:
   ```bash
   echo "127.0.0.1 fl-nextjs-core.local" | sudo tee -a /etc/hosts
   ```
3. **Middlewares de WebSocket**: Si tienes problemas, comenta temporalmente los middlewares de WebSocket en el docker-compose.yml.

### Hot Reload funcionando ✅
El hot reload está completamente funcional con la configuración actual:
- Los cambios en archivos se detectan automáticamente
- La recompilación es instantánea
- Los WebSockets funcionan correctamente a través de Traefik
- No se requiere configuración adicional

## Comandos Útiles

```bash
# Ver logs
docker-compose logs -f

# Reconstruir sin cache
docker-compose build --no-cache

# Limpiar contenedores e imágenes
docker-compose down --rmi all --volumes

# Ejecutar comandos dentro del contenedor
docker-compose exec fl-nextjs-core sh

# Probar conectividad
curl -H "Host: fl-nextjs-core.local" http://localhost

# Verificar estado de Traefik
curl http://localhost:8080/api/http/services
```
