version: '3.8'

services:
  cbqnq:
    build:
      context: .
      target: production
    container_name: ${SERVICE_NAME:-cbqnq-prod}
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${SERVICE_NAME:-cbqnq-prod}.rule=Host(`${TRAEFIK_HOST:-cbqnq.prod}`)"
      - "traefik.http.routers.${SERVICE_NAME:-cbqnq-prod}.entrypoints=${TRAEFIK_ENTRYPOINT:-websecure}"
      - "traefik.http.services.${SERVICE_NAME:-cbqnq-prod}.loadbalancer.server.port=3000"
      # Production: Basic security headers (SSL disabled for this Traefik instance)
      - "traefik.http.middlewares.${SERVICE_NAME:-cbqnq-prod}-security.headers.frameDeny=true"
      - "traefik.http.middlewares.${SERVICE_NAME:-cbqnq-prod}-security.headers.contentTypeNosniff=true"
      - "traefik.http.middlewares.${SERVICE_NAME:-cbqnq-prod}-security.headers.browserXssFilter=true"
      - "traefik.http.routers.${SERVICE_NAME:-cbqnq-prod}.middlewares=${SERVICE_NAME:-cbqnq-prod}-security"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://0.0.0.0:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  default:
    external:
      name: ${DOCKER_NETWORK:-production}
