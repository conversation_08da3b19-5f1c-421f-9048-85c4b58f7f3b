version: '3.8'

services:
  cbqnq:
    build:
      context: .
      target: ${DOCKER_TARGET:-dev}
    container_name: ${SERVICE_NAME:-cbqnq}
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - NEXT_TELEMETRY_DISABLED=1
    volumes:
      # Only mount source code in development mode, excluding node_modules
      - ./src:/app/src:rw,cached
      - ./public:/app/public:rw,cached
      - ./package.json:/app/package.json:ro
      - ./yarn.lock:/app/yarn.lock:ro
      - ./next.config.ts:/app/next.config.ts:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      - ./tailwind.config.js:/app/tailwind.config.js:ro
      - ./postcss.config.mjs:/app/postcss.config.mjs:ro
      - ./eslint.config.mjs:/app/eslint.config.mjs:ro
      - ./.env:/app/.env:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${SERVICE_NAME:-cbqnq}.rule=Host(`${TRAEFIK_HOST:-cbqnq.local}`)"
      - "traefik.http.routers.${SERVICE_NAME:-cbqnq}.entrypoints=${TRAEFIK_ENTRYPOINT:-web}"
      - "traefik.http.services.${SERVICE_NAME:-cbqnq}.loadbalancer.server.port=3000"
      # Enable WebSocket and hot reload support
      - "traefik.http.services.${SERVICE_NAME:-cbqnq}.loadbalancer.passhostheader=true"
      - "traefik.http.services.${SERVICE_NAME:-cbqnq}.loadbalancer.sticky=false"
    restart: unless-stopped

networks:
  default:
    external:
      name: ${DOCKER_NETWORK:-devel}